import { selfieImageRepo } from "../repositories";
import { SelfieSettingEntity } from "../types";
import * as contractService from "./contract-service";
import * as common from "../utils/common";

// FLAGS
const FLAG_FACE = 1;
const FLAG_ID_CARD = 1 << 1;
const FLAG_LOCATION = 1 << 2;

let selfieSettings: SelfieSettingEntity[] = [];

export async function reloadConfig() {
    selfieSettings = await selfieImageRepo.getSelfieSettings();
}

function hasFlag(value: number, flag: number) {
    return (value & flag) !== 0;
}

export function mustCheckFace(partnerCode: string) {
    let index = selfieSettings.findIndex((setting) => setting.partner_code === partnerCode && hasFlag(setting.enabled, FLAG_FACE));
    return index >= 0;
}

export function mustCheckIdCard(partnerCode: string) {
    let index = selfieSettings.findIndex((setting) => setting.partner_code === partnerCode && hasFlag(setting.enabled, FLAG_ID_CARD));
    return index >= 0;
}

export function mustCheckLocation(partnerCode: string) {
    let index = selfieSettings.findIndex((setting) => setting.partner_code === partnerCode && hasFlag(setting.enabled, FLAG_LOCATION));
    return index >= 0;
}

export async function detectBlockSelfie(contractNumber: string) {
    let selfies = await selfieImageRepo.getRecentSelfie(contractNumber, 5);
    let yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    selfies = selfies.filter(x => x.created_date > yesterday);
    if (selfies.length === 5) {
        let block = true;
        for (const item of selfies) {
            if (item.status === "SUCCESS" || item.status === "MANUAL" || item.status === "MANUAL_PASS") {
                block = false;
                break;
            }
        }
        if (block) {
            await selfieImageRepo.updateSelfieStatus(selfies[0].id, "BLOCK");
        }
        return block;
    } else {
        for (const item of selfies) {
            if (item.status === "BLOCK") {
                await selfieImageRepo.updateSelfieStatus(selfies[0].id, "BLOCK");
                return true;
            }
        }
    }
    return false;
}

export async function manualPass(id: number, status: string) {
    await selfieImageRepo.updateSelfieStatus(id, status);
}

export async function checkBlockSelfie(contractNumber: string) {
    let selfies = await selfieImageRepo.getRecentSelfie(contractNumber, 5);
    let block = false;
    let yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    for (const item of selfies) {
        if (item.status === "BLOCK" && item.created_date.getTime() > yesterday.getTime()) {
            block = true;
            break;
        }
    }
    return block;
}

export async function validateTokenFaceCheck(contractNumber: string, token: string) {
    let contract = await contractService.findContractInfoByContractNumber(contractNumber);
    if (contract) {
        if (contract.flags.faceCheck) {
            let selfies = await selfieImageRepo.getSelfieByToken(contractNumber, token);
            if (selfies?.length > 0) {
                for (const item of selfies) {
                    if (item.status === "SUCCESS" || item.status === "MANUAL_PASS") {
                        return true;
                    }
                }
            }
            return false;
        }
    }
    return true;
}

export async function updatePartners(partners: { code: string, enabled: number }[]) {
    for (const item of partners) {
        common.log(`Add face verify config: ` + JSON.stringify(item));
        await selfieImageRepo.addSelfieSetting(item.code, item.enabled);
    }
}