import aws, { S3 } from "aws-sdk";
import * as common from "../utils/common";

export const BUCKET_NAME = "uat-esign-evn";
const MC_LOS_BUCKET_NAME = "ms-los-ap-southeast-1-446567516155-document";
const LOS_BUCKET_NAME = "ms-los-ap-southeast-1-446567516155-document";

const s3 = new aws.S3({
    accessKeyId: '********************',
    secretAccessKey: 'YPe5QuYSVtRF2L5j6PJ+I1av9iOMyGsqTd5HPPHx'
});
const s3Los = new aws.S3({
    accessKeyId: '********************',
    secretAccessKey: '07NUM3l2axjBzdRpUaDo5wZDaUiVxhrrq9lmTwgt'
});
aws.config.update({
    region: 'ap-southeast-1',
});


export async function uploadFile(fileName: string, fileData: Buffer) {
    common.log("Upload file: " + fileName);
    return new Promise<S3.ManagedUpload.SendData>((resolve, reject) => {
        s3.upload({ Bucket: BUCKET_NAME, Key: fileName, Body: fileData }, (err, data) => {
            if (err) {
                common.log("Upload file failed");
                reject(err);
            } else if (data) {
                common.log("Upload file successfully");
                resolve(data);
            }
        })
    });
}

export async function uploadFileV2(fileName: string, fileData: Buffer) {
    common.log("Upload file: " + fileName);
    return new Promise<S3.ManagedUpload.SendData>((resolve, reject) => {
        s3Los.upload({ Bucket: MC_LOS_BUCKET_NAME, Key: fileName, Body: fileData }, (err, data) => {
            if (err) {
                common.log("Upload file failed");
                reject(err);
            } else if (data) {
                common.log("Upload file successfully");
                resolve(data);
            }
        })
    });
}

export async function downloadFile(fileName: string) {
    common.log("Download file: " + fileName);
    return new Promise<Buffer>((resolve, reject) => {
        s3.getObject({ Bucket: BUCKET_NAME, Key: fileName }, (err, data) => {
            if (err) {
                common.log("Download file failed");
                reject(err);
            } else if (data) {
                common.log("Download file successfully");
                resolve(data.Body as Buffer);
            }
        })
    });
}

export async function downloadLosFile(fileName: string) {
    common.log("Download file: " + fileName);
    return new Promise<Buffer>((resolve, reject) => {
        s3.getObject({ Bucket: MC_LOS_BUCKET_NAME, Key: fileName }, (err, data) => {
            if (err) {
                common.log("Download file failed");
                reject(err);
            } else if (data) {
                common.log("Download file successfully");
                resolve(data.Body as Buffer);
            }
        })
    });
}

export async function downloadLosUnitedFile(fileName: string) {
    common.log("Download file: " + fileName);
    return new Promise<Buffer>((resolve, reject) => {
        s3.getObject({ Bucket: LOS_BUCKET_NAME, Key: fileName }, (err, data) => {
            if (err) {
                common.log("Download file failed");
                reject(err);
            } else if (data) {
                common.log("Download file successfully");
                resolve(data.Body as Buffer);
            }
        })
    });
}