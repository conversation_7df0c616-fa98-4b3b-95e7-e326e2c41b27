import * as contractRuleRepo from '../repositories/contract-rules-repo';
import * as termsAndConditionsRepo from '../repositories/terms-conditions-repo';
import * as contractInfoRepo from '../repositories/contract-info-repo';
import { ContractInfoLosResponse, ContractInfoStatus, ContractRuleChosenItemRequest, ContractRuleItem, TermsAndConditionsDTO } from '../types';
import * as common from "../utils/common";
import { getContractSystemType } from '../utils/contract-utils';
import { losClApi, losUnitedApi } from '../api';
import { abTestingService } from '.';

export async function getListContractRule(contractNumber: string): Promise<ContractRuleItem[]> {
    let serviceName = 'MCA';
    let systemType = getContractSystemType(contractNumber);
    if (systemType === "vinfast") {
        serviceName = "VIF";
    } else if (systemType === "cashloan" || systemType === "united") {
        serviceName = "CL";
        let contractInfo = systemType === "cashloan"
            ? await losClApi.contractInfo(contractNumber)
            : await losUnitedApi.contractInfo(contractNumber);
        let contractType = contractInfo?.data?.contractType ? parseInt(contractInfo.data.contractType.toString()) : -1;
        if (contractType == 2) serviceName = 'MCL';
    }
    let resultData = await contractRuleRepo.getListContractRule(serviceName);
    return resultData.rows.map(record => ({
        ruleId: record.rule_id,
        ruleName: record.rule_name,
        ruleContent: record.rule_content,
        defaultAgree: record.default_agree,
        allowedEdit: record.allowed_edit,
        keySelection: record.key_selection,
    }));
}

export async function getListContractRuleChosen(contractNumber: string): Promise<ContractRuleItem[]> {
    let resultData = await contractRuleRepo.getListContractRuleChosen(contractNumber);
    return resultData.rows.map(row => ({
        ruleId: row.rule_id,
        ruleName: row.rule_name,
        ruleContent: row.rule_content,
        defaultAgree: row.agreed,
        keySelection: row.key_selection,
        allowedEdit: false
    }));
}

export async function insertContractRuleChosenList(contractNumber: string, contractRuleChosenList: ContractRuleChosenItemRequest[]) {
    let result = await contractRuleRepo.insertContractRuleChosenList(contractNumber, contractRuleChosenList);
    common.log("insertContractRuleChosenList: " + result);
}

export async function updateContractRuleChosenList(contractNumber: string, contractRuleChosenList: ContractRuleChosenItemRequest[]) {
    let ruleMap: Record<string, ContractRuleChosenItemRequest & { exists: boolean }> = {};
    contractRuleChosenList.forEach(x => ruleMap[x.ruleId] = { ...x, exists: false });
    let existedRows = await contractRuleRepo.getListContractRuleChosen(contractNumber);
    let updateList: ContractRuleChosenItemRequest[] = [];
    for (const row of existedRows.rows) {
        const newRule = ruleMap[row.rule_id];
        if (newRule) {
            if (newRule.agreed != row.agreed) {
                updateList.push(newRule);
            }
            newRule.exists = true;
        }
    }
    let insertList = contractRuleChosenList.filter(x => !ruleMap[x.ruleId].exists);
    if (insertList.length > 0)
        await contractRuleRepo.insertContractRuleChosenList(contractNumber, insertList);
    if (updateList.length > 0)
        await contractRuleRepo.updateContractRuleChosenList(contractNumber, updateList);

    if (insertList.length + updateList.length > 0)
        return await getListContractRuleChosen(contractNumber);
    else return existedRows.rows.map(row => ({
        ruleId: row.rule_id,
        ruleName: row.rule_name,
        ruleContent: row.rule_content,
        defaultAgree: row.agreed,
        keySelection: row.key_selection,
        allowedEdit: false
    }));
}

export async function getListTermsConditions() {
    let termsAndConditionsList: TermsAndConditionsDTO[] = [];
    let resultData = await termsAndConditionsRepo.getListTermsConditions();
    for (let i in resultData.rows) {
        let record = resultData.rows[i];
        let termsAndConditions: TermsAndConditionsDTO = {
            termId: record.term_id,
            termContent: record.agree_content,
            defaultAgree: record.default_agree,
            allowedEdit: record.allowed_edit
        };
        termsAndConditionsList.push(termsAndConditions);
    }
    return termsAndConditionsList;
}

export async function getListTermsConditionsAgreed(contractNumber: string) {
    let termsAndConditionsList: TermsAndConditionsDTO[] = [];
    let resultData = await termsAndConditionsRepo.getListTermsConditionsAgreed(contractNumber);
    for (let i in resultData.rows) {
        let record = resultData.rows[i];
        let termsAndConditions: TermsAndConditionsDTO = {
            termId: record.term_id,
            termContent: record.agree_content,
            defaultAgree: record.default_agree,
            allowedEdit: false
        };
        termsAndConditionsList.push(termsAndConditions);
    }
    return termsAndConditionsList;
}

export async function insertTermsConditionsAgreed(contractNumber: string, termsAndConditions: { termId: string, agreed: boolean }[]) {
    let result = await termsAndConditionsRepo.insertTermsConditionsAgreed(contractNumber, termsAndConditions);
    common.log(result);
}

export async function insertContractInfo(losResponse: ContractInfoLosResponse) {
    let customerAddress = losResponse.data.temDetailAddress + ', ' + losResponse.data.temWard + ', ' + losResponse.data.temDistrict + ', ' + losResponse.data.temProvince;
    let [flags, force] = abTestingService.generateFlags(losResponse.data.partnerCode);
    let contractInfo = {
        contract_number: losResponse.data.contractNumber,
        customer_name: losResponse.data.customerName,
        identity_card_id: losResponse.data.idCardNumber,
        phone_number: losResponse.data.phoneNumber,
        customer_address: customerAddress,
        state_province: losResponse.data.permanentProvince,
        partner_code: losResponse.data.partnerCode,
        status_code: losResponse.data.status as ContractInfoStatus,
        flags,
        spic_path: losResponse.data.pathSpic,
        request_id: losResponse.data.requestId,
        pid_path: losResponse.data.pidDocs?.find(x => x.doc_type === 'PID')?.file_path,
        pid1_path: losResponse.data.pidDocs?.find(x => x.doc_type === 'PID1')?.file_path,
    };
    return await contractInfoRepo.insertInfo(contractInfo, force);
}

export async function findContractInfoByContractNumber(contractNumber: string) {
    let resultData = await contractInfoRepo.findByContractNumber(contractNumber);
    return resultData;
}

export async function saveListDocs(contractNumber: string, response: string, status: number) {
    await contractInfoRepo.saveListDocs(contractNumber, response, status);
}
export async function getListDocs(contractNumber: string) {
    let resultData = await contractInfoRepo.getListDocs(contractNumber);
    return resultData.rows?.[0];
}
export async function updateListDocs(contractNumber: string) {
    await contractInfoRepo.updateListDocs(contractNumber);
}

// export async function insertContractAnnexInfo(info: ContractAnnexInfoEntity) {
//     // Validate contract_annex_info
//     info.contract_date = new Date(info.contract_date);
//     info.status_code ??= "SIGNING";

//     await contractAnnexInfoRepo.insertInfo(info);
// }

// export async function findContractAnnexByContractNumber(contractNumber: string) {
//     let resultData = await contractAnnexInfoRepo.findByContractNumber(contractNumber);
//     return resultData;
// }