import * as prepareCertificateRepo from '../repositories/prepare-certificate-repo';
import * as prepareFileRepo from '../repositories/prepare-file-repo';
import * as authorizeCounterSigningRepo from '../repositories/authorize-counter-signing-repo';
import * as regenerateAuthorizationCodeRepo from '../repositories/regenerate-authorization-code-repo';
import * as fileDataSignedRepo from '../repositories/file-data-signed-repo';
import * as common from '../utils/common';
import { AuthorizeCounterSigningEntity, ContractDbrInfo, ContractInfoEntity, FileDataSignedEntity, FptResponse, RegenerateAuthorizationCodeEntity, ResponseUpload } from '../types';
import { getContractSystemType, uploadSignedDbrFiles } from '../utils/contract-utils';
import * as documentService from "../services/document-service";
import { convertServiceApi, fptEsignApi, losApi, losClApi, losMcApi, losUnitedApi, losVfApi } from '../api';
import * as fileService from './file-service';
import { contractInfoRepo } from '../repositories';
import { exec } from "node:child_process";
import { nanoid } from 'nanoid';
import fs from "fs";

export async function customerRegister(contractInfoEntity: ContractInfoEntity, fileData: string): Promise<{ status: string; msg: string; authorizeCode?: string; }> {
    let status = null;
    let msg = null;
    let authorizeCode = null;
    let agreementUUID = generateAgreementUUID(contractInfoEntity.contract_number);
    let contractInfo = {
        customerName: contractInfoEntity.customer_name,
        contractNumber: contractInfoEntity.contract_number,
        identityCardId: contractInfoEntity.identity_card_id,
        phoneNumber: contractInfoEntity.phone_number,
        customerAddress: contractInfoEntity.customer_address,
        images: {
            idFront: contractInfoEntity.pid_path,
            idBack: contractInfoEntity.pid_path,
            portrait: contractInfoEntity.spic_path,
        },
    };
    let fptPrepareCertificateResponse = await fptEsignApi.prepareCertificateForSignCloud(agreementUUID, contractInfo);

    if (fptPrepareCertificateResponse == null) {
        let result = {
            status: 'fail',
            msg: 'Lỗi khi kí hợp đồng, vui lòng thử lại sau'
            // authorizeCode : authorizeCode
        };
        return result;
    }

    await insertPrepareCertificate(agreementUUID, contractInfoEntity, fptPrepareCertificateResponse);
    if (fptPrepareCertificateResponse.responseCode == 0) {
        //preprare file contract
        let resultPrepareFile = await prepareFile(agreementUUID, contractInfoEntity.contract_number, fileData, contractInfoEntity.partner_code);
        status = resultPrepareFile.status;
        msg = resultPrepareFile.msg;
        authorizeCode = resultPrepareFile.authorizeCode;
    } else {
        status = 'fail';
        msg = 'Lỗi khi kí hợp đồng, vui lòng thử lại sau';
    }
    let result = {
        status: status,
        msg: msg,
        authorizeCode: authorizeCode
    };
    return result;
}

export async function customerRegisterDr(info: ContractDbrInfo, fileData: string, contractToken: string): Promise<{ status: string; msg: string; authorizeCode?: string; }> {
    let status = null;
    let msg = null;
    let authorizeCode = null;
    let agreementUUID = generateAgreementUUID(contractToken);
    let contractInfo = {
        customerName: info.customer_name,
        contractNumber: info.contract_number,
        identityCardId: info.identity_card,
        phoneNumber: info.phone_number,
        customerAddress: info.tem_address
    };
    let fptPrepareCertificateResponse = await fptEsignApi.prepareCertificateForSignCloud(agreementUUID, contractInfo);

    if (fptPrepareCertificateResponse == null) {
        let result = {
            status: 'fail',
            msg: 'Lỗi khi kí hợp đồng, vui lòng thử lại sau'
            // authorizeCode : authorizeCode
        };
        return result;
    }
    await insertPrepareCertificate(agreementUUID, {
        contract_number: contractToken,
        customer_address: info.tem_address,
        customer_name: info.customer_name,
        identity_card_id: info.identity_card,
        partner_code: null,
        phone_number: info.phone_number,
        state_province: info.current_province,
        status_code: "SIGNING_IN_PROGRESS",
        flags: {},
        spic_path: null,
        request_id: null,
    }, fptPrepareCertificateResponse);
    if (fptPrepareCertificateResponse.responseCode == 0) {
        //preprare file contract
        let resultPrepareFile = await prepareFile(agreementUUID, contractToken, fileData);
        status = resultPrepareFile.status;
        msg = resultPrepareFile.msg;
        authorizeCode = resultPrepareFile.authorizeCode;
    } else {
        status = 'fail';
        msg = 'Lỗi khi kí hợp đồng, vui lòng thử lại sau';
    }
    let result = {
        status: status,
        msg: msg,
        authorizeCode: authorizeCode
    };
    return result;
}

export async function prepareFile(agreementUUID: string, contractToken: string, fileData: string, partnerCode?: string) {
    let status: string;
    let msg: string;
    let authorizeCode: string;
    let fptPrepareFileResponse = await fptEsignApi.prepareFileForSignCloud(agreementUUID, fileData, contractToken, partnerCode);
    common.log('prepareFile');
    await insertPrepareFile(agreementUUID, contractToken, fptPrepareFileResponse);
    common.log(fptPrepareFileResponse.responseCode);
    if (fptPrepareFileResponse.responseCode == 1007) {
        status = 'success';
        msg = 'register success';
        authorizeCode = fptPrepareFileResponse.authorizeCredential;
        let authorizationCodeInfo = {
            agreement_uuid: agreementUUID,
            authorize_code: authorizeCode,
            contract_number: contractToken,
            response_code: 0,
            response_message: ''
        };
        // console.log(authorizationCodeInfo);
        await insertAuthorizationCodeInfo(authorizationCodeInfo);
    } else {
        status = 'fail';
        msg = 'Lỗi khi kí hợp đồng, vui lòng thử lại sau';
    }
    let result = {
        status: status,
        msg: msg,
        authorizeCode: authorizeCode
    };
    return result;
}

export async function regenerateAuthorizationCode(agreementUUID: string, contractToken: string) {
    let status: string;
    let msg: string;
    let authorizeCode: string;
    let fptResponse = await fptEsignApi.regenerateAuthorizationCodeForSignCloud(agreementUUID);
    if (fptResponse.responseCode == 0) {
        status = 'success';
        msg = 'Gửi OTP thành công';
        authorizeCode = fptResponse.authorizeCredential;
    } else if (fptResponse.responseCode == 1005) {
        status = 'fail';
        msg = 'Gửi OTP không thành công, hãy thử lại sau 5 phút'
    } else {
        status = 'fail';
        msg = 'Gửi OTP không thành công, hãy thử lại sau';
    }
    let authorizationCodeInfo = {
        agreement_uuid: agreementUUID,
        authorize_code: authorizeCode,
        contract_number: contractToken,
        response_code: fptResponse.responseCode,
        response_message: fptResponse.responseMessage
    };
    await insertAuthorizationCodeInfo(authorizationCodeInfo);
    let resultRegenerate = {
        status: status,
        msg: msg,
        authorizeCode: authorizeCode
    };
    return resultRegenerate;
}

export async function insertPrepareCertificate(agreementUUID: string, contractInfoEntity: ContractInfoEntity, fptResponse: FptResponse) {
    await prepareCertificateRepo.insertPrepareCertificate(agreementUUID, contractInfoEntity, fptResponse);
}

export async function insertPrepareFile(agreementUUID: string, contractNumber: string, fptResponse: FptResponse) {
    await prepareFileRepo.insertPrepareFile(agreementUUID, contractNumber, fptResponse);
}

export async function findPrepareCertificateByContractNumber(contractNumber: string) {
    return await prepareCertificateRepo.findByContractNumber(contractNumber);
}

export function generateAgreementUUID(contractNumber: string) {
    let dateNow = new Date();
    return contractNumber + '_' + dateNow.getTime();
}

export function isAgreementUUIDExipred(agreementUUIDCreatedDate: Date) {
    let currentDate = new Date();
    let diffDays = common.numDayBetween(agreementUUIDCreatedDate, currentDate);
    common.log('diffDays: ' + diffDays);
    if (diffDays >= 1) {
        return true;
    }
    return false;
}

export async function findPrepareFileSuccessByAgreementUUID(agreementUUID: string) {
    return await prepareFileRepo.findPrepareFileSuccessByAgreementUUID(agreementUUID);
}

export async function insertAuthorizeCounterSigning(signedInfo: AuthorizeCounterSigningEntity) {
    await authorizeCounterSigningRepo.insertAuthorizeCounterSigning(signedInfo);
}

export async function agreementUUIDSigned(agreementUUID: string) {
    return await authorizeCounterSigningRepo.agreementUUIDSigned(agreementUUID);
}

export async function contractNumberSigned(contractNumber: string) {
    return await authorizeCounterSigningRepo.contractNumberSigned(contractNumber);
}

export async function insertFileDataSigned(fileDataInfo: FileDataSignedEntity) {
    await fileDataSignedRepo.insertFileDataSigned(fileDataInfo);
    const signedData = fileDataInfo.signed_file_data.toString();
    var s3Key = `signed-files/${fileDataInfo.contract_number}.pdf`;
    await fileService.uploadFile(s3Key, Buffer.from(signedData, "base64"));
    await fileDataSignedRepo.updateS3Info(fileDataInfo.contract_number, s3Key);
}

export async function isContractRegistered(contractNumber: string): Promise<{ status: "success" | "fail"; msg: string; data: { agreementUUID: string; billCode: string; phoneNumber: string; }; }> {
    let status: "success" | "fail";
    let msg: string;
    let agreementUUID: string;
    let billCode: string;
    let phoneNumber: string;
    let prepareCertificateInfo = await findPrepareCertificateByContractNumber(contractNumber);
    if (prepareCertificateInfo.rowCount > 0 && prepareCertificateInfo.rows[0].response_code == 0) {
        let prepareFileInfo = await findPrepareFileSuccessByAgreementUUID(prepareCertificateInfo.rows[0].current_agreement_uuid);
        if (prepareFileInfo.rowCount > 0) {
            status = 'success';
            msg = 'contract have been registered';
            agreementUUID = prepareFileInfo.rows[0].agreement_uuid;
            billCode = prepareFileInfo.rows[0].bill_code;
            phoneNumber = prepareCertificateInfo.rows[0].mobile_no;
        } else {
            status = 'fail';
            msg = 'Hợp đồng chưa được đăng kí';
        }
    } else {
        status = 'fail';
        msg = 'Hợp đồng chưa được đăng kí';
    }
    let result = {
        status: status,
        msg: msg,
        data: {
            agreementUUID: agreementUUID,
            billCode: billCode,
            phoneNumber: phoneNumber
        }
    };
    return result;
}

let status = null;
export async function customerSign(contractNumber: string, agreementUUID: string, authorizeCode: string, billCode: string, { signEC = true, newFileHost = false }: { signEC?: boolean; newFileHost?: boolean } = {}): Promise<{ status: string; msg: string; responseUpload?: any; }> {
    let msg = null;
    let ecSignedResponseCode = null;
    let ecSignedResponseMessage = null;
    let customerSignedResponseCode = null;
    let customerSignedResponseMessage = null;

    const systemType = getContractSystemType(contractNumber);
    const contractInfo = await contractInfoRepo.findByContractNumber(contractNumber);

    let resultAgreementUUIDSigned = await agreementUUIDSigned(agreementUUID);
    let responseUpload: ResponseUpload;
    if (!resultAgreementUUIDSigned) {
        common.log('Signing_contract:');
        let resultGetSignedFile = await fptEsignApi.getSignedFileForSignCloud(agreementUUID);
        common.log('Signed_contract:');
        if (resultGetSignedFile.responseCode == 0 && resultGetSignedFile.multipleSignedFileData.length > 0) {
            common.log('Upload_signed_contract_1:');
            let buffer: Buffer;
            if (signEC) {
                let resultECSign = await prepareFileForEasyCreditSign(contractNumber, agreementUUID, resultGetSignedFile.multipleSignedFileData[0].signedFileData, contractInfo.partner_code);
                if (resultECSign.status === 'success') {
                    common.log('Upload_signed_contract_success_1:');
                    buffer = Buffer.from(resultECSign.signedFileDataAll, "base64");
                    //upload signed file to los service
                }
                status = resultECSign.status;
                msg = resultECSign.msg;
                ecSignedResponseCode = resultECSign.ecSignedResponseCode;
                ecSignedResponseMessage = resultECSign.ecSignedResponseMessage;
            } else {
                buffer = Buffer.from(resultGetSignedFile.multipleSignedFileData[0].signedFileData, "base64");
                status = 'success';
                msg = 'Hợp đồng đã được ký thành công';
            }
            if (systemType === "credit") {
                responseUpload = await losApi.uploadSignedFileContract(contractNumber, buffer);
            } else if (systemType === "merchant") {
                responseUpload = await losMcApi.uploadSignedFileContract(contractNumber, buffer);
            } else if (systemType === "vinfast") {
                responseUpload = await losVfApi.uploadSignedFileContract(contractNumber, buffer);
            } else if (systemType === "cashloan") {
                responseUpload = await losClApi.uploadSignedFileContract(contractNumber, buffer);
            } else if (systemType === "united") {
                responseUpload = await losUnitedApi.uploadSignedFileContract(contractNumber, buffer);
            }
            status = resultGetSignedFile.status;
            msg = resultGetSignedFile.msg;
            customerSignedResponseCode = resultGetSignedFile.responseCode;
            customerSignedResponseMessage = resultGetSignedFile.responseMessage;
            common.log('end customerSignedResponse:');
        } else {
            common.log('Upload_signed_contract_2:');
            let fptAuthorizeCounterSigningResponse = await fptEsignApi.authorizeCounterSigningForSignCloud(agreementUUID, authorizeCode, billCode);
            if (!fptAuthorizeCounterSigningResponse) {
                return {
                    status: 'fail',
                    msg: 'Lỗi khi ký hợp đồng, vui lòng thử lại sau'
                };
            }
            if (fptAuthorizeCounterSigningResponse.responseCode == 1004) {
                return {
                    status: 'fail',
                    msg: 'Mã OTP không hợp lệ, vui lòng kiểm tra lại'
                };
            }
            if (fptAuthorizeCounterSigningResponse.responseCode == 1005) {
                return {
                    status: 'fail',
                    msg: 'Mã OTP không hợp lệ, hãy thử lại sau 5 phút'
                };
            }
            if (fptAuthorizeCounterSigningResponse.responseCode == 1006) {
                return {
                    status: 'fail',
                    msg: 'Mã OTP đã hết hạn, vui lòng kiểm tra lại'
                };
            }
            if (fptAuthorizeCounterSigningResponse.responseCode == 0) {
                let buffer: Buffer;
                if (signEC) {
                    let resultECSign = await prepareFileForEasyCreditSign(contractNumber, agreementUUID, fptAuthorizeCounterSigningResponse.signedFileData, contractInfo.partner_code);
                    if (resultECSign.status === 'success') {
                        common.log('Upload_signed_contract_success_1:');
                        buffer = Buffer.from(resultECSign.signedFileDataAll, "base64");
                        status = resultECSign.status;
                        msg = resultECSign.msg;
                        ecSignedResponseCode = resultECSign.ecSignedResponseCode;
                        ecSignedResponseMessage = resultECSign.ecSignedResponseMessage;
                    }
                } else {
                    let fileDataInfo = {
                        agreement_uuid: agreementUUID,
                        contract_number: contractNumber + "_step1",
                        file_name: contractNumber + '_step1.pdf',
                        signed_file_data: fptAuthorizeCounterSigningResponse.signedFileData
                    };
                    await insertFileDataSigned(fileDataInfo);
                    buffer = Buffer.from(fptAuthorizeCounterSigningResponse.signedFileData, "base64");
                    status = 'success';
                    msg = 'Hợp đồng đã được ký thành công';
                }
                //upload signed file to los service
                if (newFileHost) {
                    responseUpload = await convertServiceApi.uploadFile(contractNumber, buffer);
                } else if (systemType === "credit") {
                    responseUpload = await losApi.uploadSignedFileContract(contractNumber, buffer);
                } else if (systemType === "merchant") {
                    responseUpload = await losMcApi.uploadSignedFileContract(contractNumber, buffer);
                } else if (systemType === "vinfast") {
                    responseUpload = await losVfApi.uploadSignedFileContract(contractNumber, buffer);
                } else if (systemType === "cashloan") {
                    responseUpload = await losClApi.uploadSignedFileContract(contractNumber, buffer);
                } else if (systemType === "united") {
                    responseUpload = await losUnitedApi.uploadSignedFileContract(contractNumber, buffer);
                }
            } else {
                status = 'fail';
                msg = 'Lỗi khi ký hợp đồng, vui lòng thử lại sau';
            }
            customerSignedResponseCode = fptAuthorizeCounterSigningResponse.responseCode;
            customerSignedResponseMessage = fptAuthorizeCounterSigningResponse.responseMessage;
            common.log('end customerSignedResponse:');
        }
        //save result signing
        let signedInfo = {
            agreement_uuid: agreementUUID,
            authorize_code: authorizeCode,
            bill_code: billCode,
            contract_number: contractNumber,
            ec_signed_response_code: ecSignedResponseCode,
            ec_signed_response_message: ecSignedResponseMessage,
            response_code: customerSignedResponseCode,
            response_message: customerSignedResponseMessage
        };
        if (status == "success") {
            common.log('insertAuthorizeCounterSigning:');
            await insertAuthorizeCounterSigning(signedInfo);
        }
    } else {
        status = 'fail';
        msg = 'Hợp đồng đã được ký';
    }

    let result = {
        status: status,
        msg: msg,
        responseUpload: responseUpload
    };
    return result;
}

export async function customerSignDr(contractToken: string, agreementUUID: string, authorizeCode: string, billCode: string): Promise<{ status: string; msg: string; }> {
    let status: string;
    let msg: string;
    let ecSignedResponseCode = null;
    let ecSignedResponseMessage = null;
    let customerSignedResponseCode = null;
    let customerSignedResponseMessage = null;

    let resultAgreementUUIDSigned = await agreementUUIDSigned(agreementUUID);
    if (!resultAgreementUUIDSigned) {
        let resultGetSignedFile = await fptEsignApi.getSignedFileForSignCloud(agreementUUID);
        if (resultGetSignedFile.responseCode == 0 && resultGetSignedFile.multipleSignedFileData.length > 0) {
            let resultECSign = await prepareFileForEasyCreditSign(contractToken, agreementUUID, resultGetSignedFile.multipleSignedFileData[0].signedFileData);
            status = resultECSign.status;
            msg = resultECSign.msg;
            ecSignedResponseCode = resultECSign.ecSignedResponseCode;
            ecSignedResponseMessage = resultECSign.ecSignedResponseMessage;
            customerSignedResponseCode = resultGetSignedFile.responseCode;
            customerSignedResponseMessage = resultGetSignedFile.responseMessage;
            common.log('end customerSignedResponse:');
        } else {
            let fprAuthorizeCounterSigningResponse = await fptEsignApi.authorizeCounterSigningForSignCloud(agreementUUID, authorizeCode, billCode);
            if (!fprAuthorizeCounterSigningResponse) {
                let result = {
                    status: 'fail',
                    msg: 'Lỗi khi kí hợp đồng, vui lòng thử lại sau'
                };
                return result;
            }
            if (fprAuthorizeCounterSigningResponse.responseCode == 1004) {
                let result = {
                    status: 'fail',
                    msg: 'Mã OTP không hợp lệ, vui lòng kiểm tra lại'
                };
                return result;
            }
            if (fprAuthorizeCounterSigningResponse.responseCode == 0) {
                let resultECSign = await prepareFileForEasyCreditSign(contractToken, agreementUUID, fprAuthorizeCounterSigningResponse.signedFileData);
                status = resultECSign.status;
                msg = resultECSign.msg;
                ecSignedResponseCode = resultECSign.ecSignedResponseCode;
                ecSignedResponseMessage = resultECSign.ecSignedResponseMessage;
            } else {
                status = 'fail';
                msg = 'Lỗi khi kí hợp đồng, vui lòng thử lại sau';
            }
            customerSignedResponseCode = fprAuthorizeCounterSigningResponse.responseCode;
            customerSignedResponseMessage = fprAuthorizeCounterSigningResponse.responseMessage;
            common.log('end customerSignedResponse:');
        }
        //save result signing
        let signedInfo = {
            agreement_uuid: agreementUUID,
            authorize_code: authorizeCode,
            bill_code: billCode,
            contract_number: contractToken,
            ec_signed_response_code: ecSignedResponseCode,
            ec_signed_response_message: ecSignedResponseMessage,
            response_code: customerSignedResponseCode,
            response_message: customerSignedResponseMessage
        };
        common.log('insertAuthorizeCounterSigning:');
        await insertAuthorizeCounterSigning(signedInfo);
    } else {
        status = 'fail';
        msg = 'Hợp đồng đã được ký';
    }

    let result = {
        status: status,
        msg: msg,
    };
    return result;
}

export async function insertAuthorizationCodeInfo(authorizationCodeInfo: RegenerateAuthorizationCodeEntity) {
    await regenerateAuthorizationCodeRepo.insertInfo(authorizationCodeInfo);
}

export async function findAuthorizationCodeInfoByAgreementUUID(agreementUUID: string) {
    let resultData = await regenerateAuthorizationCodeRepo.findByAgreementUUID(agreementUUID);
    return resultData.rows?.[0];
}
export async function findOptSend(agreementUUID: string, contractNumber: string) {
    let resultData = await regenerateAuthorizationCodeRepo.findOptSend(contractNumber);
    let number = resultData?.rowCount ?? 0;
    if (number > 5) {
        await regenerateAuthorizationCodeRepo.insertLock(agreementUUID, contractNumber);
        return 1;
    }
    return 0;
}
export async function findOpt(contractToken: string) {
    let resultData = await regenerateAuthorizationCodeRepo.findOptSend(contractToken);
    if (resultData.rowCount > 0) {
        return resultData.rows?.[resultData.rowCount - 1];
    }
}
export async function getLockContract(contractNumber: string) {
    let resultData = await regenerateAuthorizationCodeRepo.getLockContract(contractNumber);
    if (resultData && resultData.rowCount && resultData.rowCount > 0) {
        return true;
    }
    return false;
}
export async function findOtpDue(contractNumber: string) {
    let resultData = await regenerateAuthorizationCodeRepo.findOtpDue(contractNumber);
    if (resultData && resultData.rowCount && resultData.rowCount > 0) {
        return true;
    }
    return false;
}


export async function prepareFileForEasyCreditSign(contractToken: string, agreementUUID: string, signedFileDataCustomer: string, partnerCode?: string): Promise<{ status: string; msg: string; signedFileDataAll?: string; ecSignedResponseCode?: any; ecSignedResponseMessage?: any; }> {
    let status: string;
    let msg: string;
    let signedFileDataAll = null;
    let prepareFileForECResponse = await fptEsignApi.prepareFileForEasyCreditSignCloud(signedFileDataCustomer, contractToken, partnerCode);
    if (!prepareFileForECResponse) {
        let result = {
            status: 'fail',
            msg: 'Lỗi khi kí hợp đồng, vui lòng thử lại sau'
        };
        return result;
    }

    if (prepareFileForECResponse.responseCode == 0 || prepareFileForECResponse.responseCode == 1018) {
        status = 'success';
        msg = 'Hợp đồng đã được ký thành công';
        signedFileDataAll = prepareFileForECResponse.signedFileData;
        let fileDataInfo = {
            agreement_uuid: agreementUUID,
            contract_number: contractToken,
            file_name: contractToken + '.pdf',
            signed_file_data: prepareFileForECResponse.signedFileData
        };
        await insertFileDataSigned(fileDataInfo);
        if (contractToken.includes("_LTT")) {
            let contractNumber = contractToken.substring(0, contractToken.length - 4);
            let signedAnnex = await documentService.getSignedAnnexFile(contractNumber);
            let signedSchedule = await documentService.getSignedScheduleFile(contractNumber);
            await uploadSignedDbrFiles(contractNumber, signedAnnex, signedSchedule);
        }
    } else {
        status = 'fail';
        msg = 'Lỗi khi kí hợp đồng, vui lòng thử lại sau';
    }

    let result = {
        status: status,
        msg: msg,
        signedFileDataAll: signedFileDataAll,
        ecSignedResponseCode: prepareFileForECResponse.responseCode,
        ecSignedResponseMessage: prepareFileForECResponse.responseMessage
    };
    return result;
}

export async function findFileSignedData(agreementUUID: string, contractNumber: string) {
    return await fileDataSignedRepo.findByAgreementUuidAndContractNumber(agreementUUID, contractNumber);
}

export async function validateContractByIdCard(contractNumber: string, idCard: string) {
    if (!idCard) return false;
    let contract = await contractInfoRepo.findByContractNumber(contractNumber);
    if (contract?.identity_card_id === idCard) {
        return true;
    } else {
        return false;
    }
}

export async function checkSignature(buffer: Buffer) {
    return new Promise((resolve, reject) => {
        try {
            let fileName = './upload_tmp/pdf/' + nanoid() + '.pdf';
            fs.mkdirSync('./upload_tmp/pdf', { recursive: true });
            fs.writeFileSync(fileName, buffer as any);
            exec(`pdfsig ${fileName} -nocert`, (err, output) => {
                try {
                    if (err) {
                        common.log("pdfsig error: " + JSON.stringify(err, Object.getOwnPropertyNames(err)));
                        resolve(undefined);
                        return;
                    }
                    const lines = output.split('\n');
                    let signatures = [];
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];
                        if (line.startsWith('Signature')) {
                            let name: string, org: string;
                            for (let j = i + 1; j < lines.length; j++) {
                                const element = lines[j];
                                if (element.startsWith('  - Signer Certificate Common Name: ')) {
                                    name = element.substring(36);
                                } else if (element.startsWith('  - Signer full Distinguished Name: ')) {
                                    org = element.substring(36).split(',').filter(x => x.startsWith('O='))[0]?.substring(2);
                                } else if (element.startsWith('Signature')) {
                                    i = j - 1;
                                    break;
                                }
                            }
                            signatures.push({ name, org });
                        }
                    }
                    common.log("pdfsig output: " + JSON.stringify(output));
                    resolve(signatures);
                } catch (error) {
                    resolve(undefined);
                } finally {
                    fs.unlinkSync(fileName);
                }
            });
        } catch (error) {
            common.error(error);
        }
        return undefined;
    });
}

export async function signEvfOnly(fileData: string, contractNumber: string, partnerCode?: string, agreementUUID?: string) {
    agreementUUID ??= generateAgreementUUID(contractNumber);
    let prepareFileForECResponse = await fptEsignApi.prepareFileForEasyCreditSignCloud(fileData, contractNumber, partnerCode);
    if (prepareFileForECResponse) {
        if (prepareFileForECResponse.responseCode == 0 || prepareFileForECResponse.responseCode == 1018) {
            let fileDataInfo = {
                agreement_uuid: agreementUUID,
                contract_number: contractNumber,
                file_name: contractNumber + '.pdf',
                signed_file_data: prepareFileForECResponse.signedFileData
            };
            insertFileDataSigned(fileDataInfo);
            return { code: 0, msg: "Success", data: prepareFileForECResponse.signedFileData };
        }
    }
    return { code: 1, msg: 'Sign failed' };
}