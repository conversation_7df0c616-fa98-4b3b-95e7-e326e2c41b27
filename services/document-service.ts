import { UploadedFile } from "express-fileupload";
import { PDFDocument, PDFImage } from "pdf-lib";
import * as fileDataSignedRepo from "../repositories/file-data-signed-repo";
import * as fileService from "./file-service";
import sizeOf from 'image-size';
import { resizeImg } from "../utils/resize-img";

export async function getSignedAnnexFile(contractNumber: string): Promise<any> {
    let contractAnnexNumber = contractNumber + "_DBR" + contractNumber;
    let annexSigned = await fileDataSignedRepo.findByContractAnnexNumber(contractAnnexNumber);
    if (!annexSigned) {
        contractAnnexNumber = contractNumber + "_DBR";
        annexSigned = await fileDataSignedRepo.findByContractAnnexNumber(contractAnnexNumber);
    }
    return annexSigned?.signed_file_data;
}

export async function getSignedScheduleFile(contractNumber: string): Promise<any> {
    let contractAnnexNumber = contractNumber + "_LTT";
    let annexSigned = await fileDataSignedRepo.findByContractAnnexNumber(contractAnnexNumber);
    if (!annexSigned)
        return null;
    return annexSigned.signed_file_data;
}

export async function getSignedContractFile(contractNumber: string): Promise<Buffer> {
    let contractSigned = await fileDataSignedRepo.findByContractNumber(contractNumber);
    if (!contractSigned)
        return null;
    if (contractSigned.s3_key) {
        let buffer = await fileService.downloadFile(contractSigned.s3_key);
        return buffer;
    } else {
        return Buffer.from(contractSigned.signed_file_data.toString(), "base64");
    }
}

export async function convertImageToPdf(selfieImg: UploadedFile): Promise<Buffer> {
    let dataFile: UploadedFile;
    const pdfDoc = await PDFDocument.create();

    let typeImg = selfieImg.mimetype && selfieImg.mimetype.includes('image/') ? selfieImg.mimetype.replace('image/', '').toLowerCase() : null;
    // let typePdf = img.mimetype && img.mimetype.includes('pdf') ? 'PDF' : null;
    let dataImage: PDFImage;
    if (typeImg?.startsWith('p')) {
        let imgNewSize = sizeOf(selfieImg.data);
        if (imgNewSize.type?.toLowerCase().startsWith('j')) {
            dataImage = await pdfDoc.embedJpg(selfieImg.data);
        } else {
            let imgData: Buffer;
            if (selfieImg.size > 900000) {
                imgData = await resizeImg(selfieImg.data, {
                    width: imgNewSize.width,
                    height: imgNewSize.height
                });
            } else imgData = selfieImg.data;
            dataImage = await pdfDoc.embedPng(imgData);
        }
    } else if (typeImg?.startsWith('j')) {
        dataImage = await pdfDoc.embedJpg(selfieImg.data);
    }
    else dataFile = selfieImg;
    if (dataImage) {
        const page = pdfDoc.addPage()
        let widthPage = page.getWidth() || 0;
        widthPage = widthPage - 40;
        let widthImg = dataImage.width || 0;
        let sizeScale = 1;
        if (widthImg > widthPage) {
            sizeScale = widthPage / widthImg;
        }
        let heightPage = page.getHeight() || 0;
        heightPage = heightPage - 40;
        let heightImg = dataImage.height || 0;
        if (heightImg > heightPage) {
            let sizeScaleHeight = heightPage / heightImg;
            if (sizeScale > sizeScaleHeight) sizeScale = sizeScaleHeight;
        }
        let jpgDims = dataImage.scale(sizeScale)
        page.drawImage(dataImage, {
            x: 20,
            y: page.getHeight() - jpgDims.height - 20,
            width: jpgDims.width,
            height: jpgDims.height,
        })
    }
    let buffer: Buffer;
    if (dataFile) {
        buffer = Buffer.from(dataFile.data.toString(), 'base64');
    } else {
        let pdfBytes = await pdfDoc.save();
        buffer = Buffer.from(pdfBytes);
    }
    return buffer;
}