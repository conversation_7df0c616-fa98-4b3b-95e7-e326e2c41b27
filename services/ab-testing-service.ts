import { abTestingRepo } from "../repositories";
import { AbTestingConfig, ContractInfoFlags } from "../types";
import * as common from "../utils/common";

let configs: AbTestingConfig[] = [];

export async function reloadConfig() {
    configs = await abTestingRepo.getAbTestingConfigs();
}

export async function updateConfigs(configs: Partial<AbTestingConfig>[]) {
    if (configs?.length > 0) {
        for (const config of configs) {
            if (config.id) {
                await abTestingRepo.updateAbTestingConfig(config);
            } else {
                await abTestingRepo.addAbTestingConfig(config);
            }
        }
    }
}

export function generateFlags(partnerCode: string): [ContractInfoFlags, boolean] {
    let now = new Date();
    let output: ContractInfoFlags = {};
    let force = false;
    for (let i = 0; i < configs.length; i++) {
        const config = configs[i];
        if (config.partner_code === partnerCode) {
            if (config.start_time && config.start_time > now) continue;
            if (config.end_time && config.end_time < now) continue;
            let branches = config.branches;
            if (branches.length === 0) {
                output = {};
            } else if (branches.length === 1) {
                output = branches[0];
                force = true;
            } else {
                let index = common.randomInt(branches.length);
                output = branches[index] ?? {};
            }
            break;
        }
    }
    common.log(output, 'AB testing rule');
    return [output, force];
}