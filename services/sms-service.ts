import * as configService from '../services/config-service';
import * as esigningService from '../services/esigning-service';
import * as smsService from '../services/sms-service';
import moment from 'moment';
import * as common from "../utils/common";
import { SmsType } from '../types';
import { caseHistoryApi, smsApi } from '../api';

const SMS_CONTENT_AUTHORIZE_CODE = '{OTP}';

export async function sendSmsOtpToSign(phoneNumber: string, authorizeCode: string, contractNumber: string, type: SmsType) {
    if (process.env.DEBUG) {
        common.log(authorizeCode);
        return;
    }
    let cloudConfig = configService.getConfig();
    let content = "";
    switch (type) {
        case "contract":
            content = cloudConfig.data.smsApi.contentSmsOtpToSign;
            break;
        case "dbr_contract":
            content = cloudConfig.data.smsApi.dbrContractSmsOtp;
            break;
        case "dbr_schedule":
            content = cloudConfig.data.smsApi.dbrScheduleSmsOtp;
            break;
    }
    content = content.replace(SMS_CONTENT_AUTHORIZE_CODE, authorizeCode);
    let request = {
        source: "EASY CREDIT",
        dest: phoneNumber,
        content: content,
        channel: "ESIGN",
    };
    let entryDate = moment().format('YYYY-MM-DD HH:mm:ss');
    let response = await smsApi.sendSms(request);
    let codeResponse = response?.code ?? "";
    let updateStatusDate = moment().format('YYYY-MM-DD HH:mm:ss');
    let requestCaseHistory = {
        contractNumber: contractNumber,
        systemType: 'STATUS_CASE_ECL',
        stepCode: 'SEND_SMS',
        actionCode: 'SEND_SMS_TO',
        entryDate: entryDate,
        updateStatusDate: updateStatusDate,
        title: phoneNumber,
        involveParty: "system",
        value: codeResponse + ':' + content
    };
    await caseHistoryApi.statusHistory(requestCaseHistory);
    return response;
}

export async function resendOtp(contractToken: string, type: SmsType, skipSms: boolean = false): Promise<{ status?: string, msg?: string, response?: any }> {
    let status: string;
    let msg: string;
    let checkContractRegisterResult = await esigningService.isContractRegistered(contractToken);
    if (checkContractRegisterResult.status === 'success') {
        // check otp resend > 5
        let cloudConfig = configService.getConfig();
        let timeLock = cloudConfig.data.fptEsignApi.timeLock;
        let baseResponseOTP = {
            status: 'FAILED_REQUEST',
            msg: 'Quá số lần gửi OTP',
            data: {
                timeLock: timeLock
            }
        };
        let checkLockContract = await esigningService.getLockContract(contractToken);
        if (checkLockContract) {
            return { response: baseResponseOTP };
        } else {
            let exceededMaximum = await esigningService.findOptSend(checkContractRegisterResult.data.agreementUUID, contractToken);
            if (exceededMaximum) return { response: baseResponseOTP };
        }
        // check Old OTP is still valid
        let checkOtpDue = await esigningService.findOtpDue(contractToken);
        if (checkOtpDue) {
            let baseResponse = {
                status: 'FAILED_REQUEST',
                msg: 'Mã OTP cũ vẫn còn hiệu lực',
            }
            return { response: baseResponse };
        }

        let resultRegenerate = await esigningService.regenerateAuthorizationCode(checkContractRegisterResult.data.agreementUUID, contractToken);
        if (resultRegenerate.status === 'success') {
            status = 'success';
            msg = 'Gửi OTP thành công';

            if (!skipSms)
                smsService.sendSmsOtpToSign(checkContractRegisterResult.data.phoneNumber, resultRegenerate.authorizeCode, contractToken, type);
        } else {
            status = resultRegenerate.status;
            msg = resultRegenerate.msg;
        }
    } else {
        status = checkContractRegisterResult.status;
        msg = checkContractRegisterResult.msg;
    }
    return { status, msg };
}