// const configService = require('../services/config-service');
import fs from 'fs';
import moment from "moment";
import { caseHistoryApi, emailApi } from '../api';

export async function sendEmailContractFile(
    receiver: string,
    subject: string,
    content: string,
    contractFileName: string,
    contractFileData: string,
    contractNumber: string,
) {
    let htmlContent = fs.readFileSync('./file_template/contract-email.html', 'utf8');
    // console.log(htmlContent);
    let request = {
        receiver: receiver,
        subject: subject,
        content: content,
        html: htmlContent,
        attachment: {
            fileName: contractFileName,
            content: contractFileData
        }
    }
    let entryDate = moment().format('YYYY-MM-DD HH:mm:ss');
    let response = await emailApi.sendEmail(request);
    let resMessage = response?.message ?? '';
    let updateStatusDate = moment().format('YYYY-MM-DD HH:mm:ss');
    let requestCaseHistory = {
        contractNumber: contractNumber,
        systemType: 'STATUS_CASE_ECL',
        stepCode: 'SEND_EMAIL',
        actionCode: 'SEND_EMAIL_TO',
        entryDate: entryDate,
        updateStatusDate: updateStatusDate,
        title: receiver,
        involveParty: "system",
        value: resMessage
    }
    await caseHistoryApi.statusHistory(requestCaseHistory);
    return response;
}

export async function sendEmailContractAnnexFile(
    receiver: string,
    subject: string,
    content: string,
    files: {
        fileName: string,
        content: string,
    }[]
) {
    let htmlContent = fs.readFileSync('./file_template/contract-annex-email.html', 'utf8');
    // console.log(htmlContent);
    let request = {
        receiver: receiver,
        subject: subject,
        content: content,
        html: htmlContent,
        attachments: files
    }
    let response = await emailApi.sendEmail(request);
    // let entryDate = moment().format('YYYY-MM-DD HH:mm:ss');
    // let resMessage = response?.message ?? '';
    // let updateStatusDate = moment().format('YYYY-MM-DD HH:mm:ss');
    // let requestCaseHistory = {
    //     contractNumber: contractNumber,
    //     systemType: 'STATUS_CASE_ECL',
    //     stepCode: 'SEND_EMAIL',
    //     actionCode: 'SEND_EMAIL_TO',
    //     entryDate: entryDate,
    //     updateStatusDate: updateStatusDate,
    //     title: receiver,
    //     involveParty: "system",
    //     value: resMessage
    // }
    // await caseHistoryApi.statusHistory(requestCaseHistory);
    return response;
}
