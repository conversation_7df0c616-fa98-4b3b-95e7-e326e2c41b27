import * as common from "../utils/common";

type DatabaseConfig = {
    user: string;
    password: string;
    port: number;
    host: string;
    database: string;
}
export type BasicService = {
    localhost: string,
    internalLb: string,
}
export type CloudConfig = {
    status: number,
    data: {
        http: {
            port: number,
        },
        options?: {
            authorization: string,
            webBlocked: string,
        },
        aaaService: {
            login: string,
            validateToken: string,
            loginV3: string,
            validateTokenV3: string,
        },
        fptEsignApi: {
            apiEcSignUrl: string;
            passCode: string;
            agreementUuid: string;
            relyingParty: string;
            relyingPartyUser: string;
            relyingPartyPassword: string;
            relyingPartySignature: string;
            relyingPartyKeyStorePassword: string;
            relyingPartyKeyStore: string;
            authorizeCodeExpireTime: number;
            serviceUrl: string;
            timeLock: number;
            timeResend: number;
        },
        oauthService: {
            password: string;
            username: string;
        },
        losCLServiceApi: {
            getContractFileUrl: string;
            getContractInfoUrl: string;
            uploadFileSignedUrl: string;
            getDocResubmit: string;
            saveUploadDoc: string;
            uploadDocs: string;
            getContractFileUrlV2?: string;
        },
        masterData: {
            docInfo: string;
        },
        losMcServiceApi: {
            getContractFileUrl: string;
            uploadFileSignedUrl: string;
            getContractInfoUrl: string;
            saveUploadDoc: string;
            uploadDocs: string;
            getDocResubmit: string;
        },
        losServiceApi: {
            serviceUrl: string;
            getContractFileUrl: string;
            getContractInfoUrl: string;
            uploadFileSignedUrl: string;
        },
        losVFServiceApi: {
            getContractInfoUrl: string;
            getContractFileUrl: string;
            contractFile: string;
            uploadFileSignedUrl: string;
        },
        smsApi: {
            contentSmsOtpToSign: string;
            dbrContractSmsOtp: string;
            dbrScheduleSmsOtp: string;
        },
        lmsCLServiceApi: {
            getPaymentSchedule: string;
        },
        losUnitedApi: {
            getContractFileUrl: string;
            getContractInfoUrl: string;
            verifyLoanUrl: string;
            cancelLoanContractUrl: string;
            refuseLoanContractUrl: string;
            uploadFileSignedUrl: string;
            getDocResubmit: string;
            saveUploadDoc: string;
            uploadDocs: string;
            getContractFileUrlV2: string;
            downloadDbrFiles: string;
            uploadSignedDbrFiles: string;
            getDbrContractInfo: string;
        },
        googleApi: {
            apiKey: string;
        }
    },
    basic: {
        crmService: BasicService,
        product: BasicService,
        aaa: BasicService,
        actionAudit: BasicService,
        bssEmailService: BasicService,
        lmsCashloan: BasicService,
        losCashloan: BasicService,
        losMcCredit: BasicService,
        CDL: BasicService,
        masterData: BasicService,
        losUnited: BasicService,
        sms: BasicService,
        decisionEngine: BasicService,
        decisionsV02: BasicService,
        antiFraud: BasicService,
        convertService: BasicService,
    }
}

let cloudConfig: CloudConfig;
let webBlockedPartners = [];
export function loadConfig(config: CloudConfig) {
    cloudConfig = config;
    webBlockedPartners = config.data.options?.webBlocked?.split(',') ?? [];
    console.log(webBlockedPartners);
    // common.log(cloudConfig);
}

export function getConfig(): CloudConfig {
    return cloudConfig;
}

export function getWebBlocked() {
    return webBlockedPartners;
}

export async function reloadConfig() {
    let cloudConfig = await common.getAPI<CloudConfig>(process.env.HOST_CONFIGURATION + "/services/?service=esigning", { 'Content-type': 'application/json' });
    common.log('[Start] Reloading config');
    if (cloudConfig == undefined || cloudConfig.status != 1) {
        common.log('Cannot reload configuration from config server');
        return false;
    } else {
        loadConfig(cloudConfig);
        common.log('[End] reload config');
        return true;
    }
}