import { Pool } from "pg";
import { CloudConfig } from "../services/config-service";
import * as common from "../utils/common";

let poolWrite: Pool;
let poolRead: Pool;
export function initPool() {
    common.log('initPool');
    poolRead = new Pool({
        host: process.env.DB_HOST_READ,
        port: parseInt(process.env.DB_PORT),
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
    });
    poolWrite = new Pool({
        host: process.env.DB_HOST_WRITE,
        port: parseInt(process.env.DB_PORT),
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
    });
}

export function getPoolWrite() {
    return poolWrite;
}

export function getPoolRead() {
    return poolRead;
}
