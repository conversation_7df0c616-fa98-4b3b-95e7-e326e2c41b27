import express, { Request, Response } from 'express';
import * as contractService from '../services/contract-service';
import * as esigningService from '../services/esigning-service';
import * as emailService from '../services/email-service';
import * as common from '../utils/common';
import { aaaApi, losClApi, losMcApi, losUnitedApi, masterDataApi } from '../api';
import { PDFDocument, PDFImage } from 'pdf-lib';
import fs from 'fs';
import sizeOf from 'image-size';
import { ContractRuleChosenItemRequest, ContractRuleInfoEntity, ContractRuleItem } from '../types';
import { callbackUpdateStatusLos, DECISION_V02_RES_DECISION, getContractFile, getContractInfo, getContractSystemType, verifyLoan } from '../utils/contract-utils';
import { configService, documentService, faceService } from '../services';
import { resizeImg } from '../utils/resize-img';

const router = express.Router();

router.post('/check', async function (request, response) {
    try {
        let status: string;
        let msg: string;
        let data = null;

        let contractNumber = (request.body.contractNumber as string)?.trim();
        let identityCard = request.body.identityCard as string;
        if (!contractNumber || !identityCard) {
            return response.status(400).json({
                status: 'fail',
                msg: 'Thiếu thông tin hợp đồng hoặc CMND/CCCD',
            });
        }
        if (identityCard.length !== 9 && identityCard.length !== 12) {
            return response.status(400).json({
                status: 'error',
                msg: 'Số CMND/CCCD không hợp lệ',
            });
        }
        let token: string;
        let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
        const systemType = getContractSystemType(contractNumber);
        let [resultContractInfo, resultVerifyLoan] = await Promise.all([
            getContractInfo(contractNumber),
            verifyLoan(contractNumber)
        ]);
        
        if (resultVerifyLoan && !resultVerifyLoan?.data?.isVerifyLoan) {
            if (resultVerifyLoan?.data?.decision && resultVerifyLoan?.data?.decision != DECISION_V02_RES_DECISION.WAIT_CIC) {
                await callbackUpdateStatusLos(contractNumber, resultVerifyLoan?.data?.decision)
            }
            return response.status(400).json({
                status: 'fail',
                msg: 'Hợp đồng không tồn tại',
            });
        }
        if (resultContractInfo?.data) {
            if (
                resultContractInfo.data.slAdvanceContractType &&
                ['EVC', 'DNSE', 'GIMO', 'FUND', 'MANF', 'VUIP', 'FINV'].includes(resultContractInfo.data.partnerCode)
            ) {
                resultContractInfo.data.partnerCode = `${resultContractInfo.data.partnerCode}_${resultContractInfo.data.slAdvanceContractType}`;
            }
            let partnerCode = resultContractInfo.data.partnerCode;
            try {
                let auth = await aaaApi.authorizeRouter(partnerCode);
                if (auth.code !== 200) {
                    return response.status(400).json({
                        status: 'fail',
                        msg: 'Hợp đồng không tồn tại',
                    });
                }
            } catch (error) { }
            if (request.body.client === "econtract")
                try {
                    if (configService.getWebBlocked().includes(partnerCode)) {
                        return response.status(400).json({
                            status: 'fail',
                            msg: 'Hợp đồng không tồn tại',
                        });
                    }
                } catch (error) { }
        }
        if (resultContractNumberSigned && resultContractInfo?.data) {
            if (resultContractInfo.data.idCardNumber == identityCard) {
                token = await aaaApi.getToken(contractNumber, identityCard);
                // Check resubmit
                if (systemType === "cashloan" || systemType === "united" || systemType === "merchant") {
                    let api: { getDocResubmit: (num: string) => Promise<any> };
                    switch (systemType) {
                        case "cashloan":
                            api = losClApi;
                            break;
                        case "united":
                            api = losUnitedApi;
                            break;
                        case "merchant":
                            api = losMcApi;
                            break;
                    }
                    let resultContractInfo2 = await api.getDocResubmit(contractNumber);
                    let codeDocResubmit = parseInt(resultContractInfo2?.code ?? "-1");
                    if (codeDocResubmit == 1) {
                        return response.status(200).json({
                            status: 'RESUBMIT',
                            msg: 'resubmit doc',
                            token: token
                        });
                    }
                }
                let listDocs = await contractService.getListDocs(contractNumber);
                if (listDocs?.response?.length) {
                    return response.status(200).json({
                        status: 'NEW',
                        msg: 'Submit doc',
                        token: token
                    });
                }
            }
            if (resultContractInfo.data.status === 'IN_CP_CHECK_DOC' || resultContractInfo.data.status === 'CP_RESUBMIT') {
                let baseResponse = {
                    status: 'signed',
                    msg: 'Hợp đồng đã được ký',
                }
                return response.status(200).json(baseResponse);
            }

            let baseResponse = {
                status: 'fail',
                msg: 'Hợp đồng đã được ký',
            }
            return response.status(400).json(baseResponse);

        }
        if (resultContractInfo && resultContractInfo.code === 'SUCCESS' && (resultContractInfo.data.status === 'SINGING_IN_PROGRESS' || resultContractInfo.data.status === 'SIGING_IN_PROGRESS' || resultContractInfo.data.status === 'SIGNING_IN_PROGRESS')) {
            if (resultContractInfo.data.idCardNumber === identityCard) {
                if (!token)
                    token = await aaaApi.getToken(contractNumber, identityCard);
                status = 'success';
                msg = 'Hợp đồng đã được tìm thấy';
                resultContractInfo.data.contractNumber = contractNumber;
                let contractInfo = await contractService.insertContractInfo(resultContractInfo);

                let selfieBlocked = await faceService.checkBlockSelfie(contractNumber);
                if (selfieBlocked) {
                    common.log("Contract blocked");
                    status = 'blocked';
                    msg = 'Hợp đồng bị khóa kí trong 24h';
                } else {
                    let contractRuleList = await contractService.getListContractRuleChosen(contractNumber);
                    if (contractRuleList.length === 0) {
                        contractRuleList = await contractService.getListContractRule(contractNumber);
                    }
                    data = {
                        contractNumber: contractNumber,
                        contractRuleList: contractRuleList,
                        token: token,
                        ...contractInfo.flags,
                    }
                    common.log('response contract:');
                    common.log(data);
                }
            } else {
                // CCCD/CMND khong dung
                if (resultContractInfo.data.partnerCode === "MCA") {
                    if (identityCard.length !== resultContractInfo.data.idCardNumber.length) {
                        return response.status(400).json({
                            status: 'error',
                            msg: 'Số CMND/CCCD chưa đúng với số đã sử dụng khi đăng ký hồ sơ vay, vui lòng thử lại với số CMND/CCCD khác',
                        });
                    }
                }
                return response.status(400).json({
                    status: 'fail',
                    msg: 'Hợp đồng không tồn tại',
                });
            }
        } else {
            status = 'fail';
            msg = 'Hợp đồng không tồn tại';
        }
        let baseResponse = {
            status: status,
            msg: msg,
            data: data
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error);
        response.status(error.status ?? 500).json(baseResponse);
    }
});

router.post('/file', async function (request: Request & { disableLog: boolean }, response) {
    common.log('api_contract/file');
    try {
        request.disableLog = true;
        let status = null;
        let msg = null;
        let data = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let identityCard = request.body.identityCard as string;
        let newFileHost = request.body.newFileHost as boolean;
        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                common.log("Invalid token: " + token);
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'invalid token',
                });
            }
        }
        if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
            common.log("Face image is not verified");
            let baseResponse = {
                status: 'face not passed',
                msg: 'face not passed',
            }
            return response.status(400).json(baseResponse);
        }
        let contractRuleChosenList = request.body.contractRuleChosenList as ContractRuleChosenItemRequest[];
        let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
        if (resultContractNumberSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'Hợp đồng đã được ký',
            }
            return response.status(400).json(baseResponse);
        }
        let contractRuleList: ContractRuleItem[] = await contractService.updateContractRuleChosenList(contractNumber, contractRuleChosenList);

        let termsAndConditionsList = await contractService.getListTermsConditionsAgreed(contractNumber);
        if (termsAndConditionsList.length === 0) {
            termsAndConditionsList = await contractService.getListTermsConditions();
        }

        let contractFileData = await getContractFile(contractNumber, contractRuleList, newFileHost);
        if (contractFileData) {
            status = 'success';
            msg = 'Hợp đồng đã được tìm thấy';
            data = {
                fileName: contractNumber + '.pdf',
                fileData: contractFileData,//set file data from los service
                contractNumber: contractNumber,
                termsAndConditions: termsAndConditionsList
            }
        } else {
            status = 'fail';
            msg = 'Không tìm thấy hợp đồng';
        }

        let baseResponse = {
            status: status,
            msg: msg,
            data: data
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('get contract file error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post('/send-email', async function (request, response) {

    let status: string;
    let msg: string;
    let token = request.headers?.token as string;
    let contractNumber = request.body.contractNumber as string;
    let identityCard = request.body.identityCard as string;
    let emailAddress = request.body.emailAddress as string;
    let validateToken = await aaaApi.validateToken(token, contractNumber);
    if (!validateToken) {
        if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
            common.log("Invalid token: " + token);
            return response.status(400).json({
                status: 'invalid token',
                msg: 'invalid token',
            });
        }
    }
    if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
        common.log("Face image is not verified");
        let baseResponse = {
            status: 'face not passed',
            msg: 'face not passed',
        }
        return response.status(400).json(baseResponse);
    }
    if (!emailAddress) {
        return response.status(400).json({
            status: 'fail',
            msg: 'Email không hợp lệ',
        });
    }
    let authorizeCounterSigningEntity = await esigningService.contractNumberSigned(contractNumber);
    if (authorizeCounterSigningEntity) {
        let contractFileBuffer: Buffer = await documentService.getSignedContractFile(contractNumber);
        if (contractFileBuffer) {
            let receiver = emailAddress;
            let subject = 'EASY CREDIT - HỢP ĐỒNG TÍN DỤNG ĐIỆN TỬ';
            let content = 'Đính kèm theo email này là Hợp đồng tín dụng điện tử cho khoản vay của Quý khách tại EASY CREDIT. Quý khách có thể xem Điều khoản và Điều kiện chung (đính kèm và là một bộ phận không thể tách rời của Hợp đồng tín dụng điện tử) theo đường link: https://easycredit.vn/dieu-khoan-va-dieu-kien-giao-dich-chung';
            let contractFileName = contractNumber + ".pdf";
            let contractFileData = contractFileBuffer.toString("base64");

            let result = await emailService.sendEmailContractFile(receiver, subject, content, contractFileName, contractFileData, contractNumber);
            if (!result) {
                let baseResponse = {
                    status: 'fail',
                    msg: 'Gửi email thất bại',
                }
                return response.status(400).json(baseResponse);
            }
            status = 'success';
            msg = 'Gửi email thành công';
        } else {
            status = 'fail';
            msg = 'Không tìm thấy hợp đồng';
        }

    } else {
        status = 'fail';
        msg = 'Hợp đồng chưa được ký';
    }

    let baseResponse = {
        status: status,
        msg: msg,
    }

    response.json(baseResponse);
});
router.options('/allow-cors', async function (req, res) {
    res.status(200).json({ message: "ok" })
})

router.post('/upload-file', async function (request, response) {
    try {
        const dir = './doc_pdf';
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true })
        }
        let contractNumber = request.body.contractNumber as string;
        let token = request.headers?.token as string;
        let identityCard = request.body.identityCard as string;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                common.log("Invalid token: " + token);
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'invalid token',
                });
            }
        }

        let systemType = getContractSystemType(contractNumber);
        let serviceApi: {
            getDocResubmit: (num: string) => Promise<any>,
            contractInfo: (num: string) => Promise<any>,
            uploadDocs: (contractNumber: string, docs, bundleList, status: string) => Promise<any>
        };
        switch (systemType) {
            case "cashloan":
                serviceApi = losClApi;
                break;
            case "united":
                serviceApi = losUnitedApi;
                break;
            case "merchant":
                serviceApi = losMcApi;
                break;
        }
        common.log('file_request: ' + contractNumber);
        let status = request.body.status ?? 'RESUBMIT';
        let filesInput = request.files;
        if (!filesInput) {
            let baseResponse = {
                status: 'fail',
                msg: 'No files input',
            }
            response.status(400).send(baseResponse);
            return;
        }
        let files = [];
        for (let [key, value] of Object.entries(filesInput)) {
            files.push(key);
        }
        let bundleList = [];
        let listDocs = [];
        if (status == 'NEW') {
            let listDocsEntity = await contractService.getListDocs(contractNumber);
            let listDocsResponse = listDocsEntity?.response;
            if (listDocsResponse) {
                bundleList = JSON.parse(listDocsResponse);
                listDocs = bundleList;
            } else {
                listDocs = [];
                bundleList = [];
            }
        } else {
            let resultContractInfo = await serviceApi.getDocResubmit(contractNumber);
            bundleList = resultContractInfo?.data ?? [];
            listDocs = bundleList;
        }
        for (let bundleObj of listDocs) {
            let docGroup = bundleObj?.docGroup ?? '';
            let listDocBunble = bundleObj && bundleObj.docList && bundleObj.docList.length ? bundleObj.docList : [];
            let docsNonDup = [];
            for (let docObj of listDocBunble) {
                let docTypeBundle = docObj?.docType ?? '';
                docObj.docGroup = docGroup;
                let docInfo = await masterDataApi.docInfo(docTypeBundle);
                let docTemp = docInfo?.data ?? {};
                docObj.docName = docTemp.valueNameVn || docTemp.valueNameEn || docTypeBundle;
                docObj.docGroup = docGroup;
                docsNonDup.push(docObj);
            }
            bundleObj.docList = docsNonDup;
        }
        bundleList = listDocs;
        // else {
        //     let resultContractInfo = await serviceApi.getDocResubmit(contractNumber);
        //     bundleList = resultContractInfo && resultContractInfo.data ? resultContractInfo.data : [];
        //     let listBundle = [];
        //     for (let bundle of bundleList) {
        //         let docListObj = [];
        //         docListObj.push(bundle);
        //         bundle = {
        //             docList: docListObj
        //         }
        //         listBundle.push(bundle);
        //     }
        //     bundleList = listBundle;
        // }
        let listFileName = [];
        let i = 1;
        for (let fileInput of files) {
            let fileSystem = request.files;
            let imgs = fileSystem[fileInput];
            if (imgs) {
                if (!Array.isArray(imgs)) {
                    imgs = [imgs];
                }
                let dataFile;
                const pdfDoc = await PDFDocument.create();

                for (let img of imgs) {
                    let typeImg = img.mimetype && img.mimetype.includes('image/') ? img.mimetype.replace('image/', '') : null;
                    // let typePdf = img.mimetype && img.mimetype.includes('pdf') ? 'PDF' : null;
                    let dataImage: PDFImage;
                    if (typeImg && (typeImg.startsWith('p') || typeImg.startsWith('P'))) {
                        let imgNewSize = sizeOf(img.data);
                        if (imgNewSize.type && (imgNewSize.type.startsWith('j') || imgNewSize.type.startsWith('J'))) {
                            dataImage = await pdfDoc.embedJpg(img.data);
                        } else {
                            let imgData: Buffer;
                            if (img.size > 900000) {
                                imgData = await resizeImg(img.data, {
                                    width: imgNewSize.width,
                                    height: imgNewSize.height
                                });
                            } else imgData = img.data;
                            dataImage = await pdfDoc.embedPng(imgData);
                        }
                    } else if (typeImg && (typeImg.startsWith('j') || typeImg.startsWith('J'))) {
                        dataImage = await pdfDoc.embedJpg(img.data);
                    }
                    else dataFile = img;
                    if (dataImage) {
                        const page = pdfDoc.addPage()
                        let widthPage = page.getWidth() ? page.getWidth() : 0;
                        widthPage = widthPage - 40;
                        let widthImg = dataImage.width ? dataImage.width : 0;
                        let sizeScale = 1;
                        if (widthImg > widthPage) {
                            sizeScale = widthPage / widthImg;
                        }
                        let heightPage = page.getHeight() ? page.getHeight() : 0;
                        heightPage = heightPage / 2;
                        let heightImg = dataImage.height ? dataImage.height : 0;
                        if (heightImg > heightPage) {
                            let sizeScaleHeight = heightPage / heightImg;
                            if (sizeScale > sizeScaleHeight) sizeScale = sizeScaleHeight;
                        }
                        let jpgDims = dataImage.scale(sizeScale)
                        page.drawImage(dataImage, {
                            x: 20,
                            y: page.getHeight() - jpgDims.height - 20,
                            width: jpgDims.width,
                            height: jpgDims.height,
                        })
                    }
                }
                let fileName = './doc_pdf/' + contractNumber + '_' + i + '.pdf';
                if (dataFile) {
                    let fileExtensions = dataFile.name.split('.').pop();
                    fileName = './doc_pdf/' + contractNumber + '_' + i + '.' + fileExtensions;
                    let pdfBytes = Buffer.from(dataFile.data, 'base64');
                    fs.writeFileSync(fileName, pdfBytes);
                } else {
                    let pdfBytes = await pdfDoc.save();
                    fs.writeFileSync(fileName, pdfBytes);
                }
                i++;
                let fileObj = {
                    fileName: fileName,
                    docType: fileInput
                }
                listFileName.push(fileObj);
            }
        }

        let data: Record<string, string> = {};
        for (let file of listFileName) {
            data[file.docType] = file.fileName;
        }
        let uploadDocs = await serviceApi.uploadDocs(contractNumber, data, bundleList, status);
        for (let file of listFileName) {
            fs.unlinkSync(file.fileName);
        }
        if (uploadDocs?.code) {
            if (status == 'NEW') await contractService.updateListDocs(contractNumber);
            response.status(200).json({ message: uploadDocs })
        } else {
            common.log('update docs error: ' + contractNumber);
            let baseResponse = {
                status: 'server_error',
                msg: 'Internal server error',
            }
            response.status(500).json(baseResponse);
        }
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        common.log('update docs error: ' + request.body?.contractNumber);
        common.error(error);
        response.status(error.status || 500).json(baseResponse);
    }

});
router.get('/list_docs', async function (request, response) {
    try {
        let contractNumber = request.query.contractNumber as string;
        let token = request.headers?.token as string;
        let identityCard = request.query.identityCard as string;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                common.log("Invalid token: " + token);
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'invalid token',
                });
            }
        }
        let status = request.query?.status as string;
        let systemType = getContractSystemType(contractNumber);
        let serviceApi: {
            getDocResubmit: (num: string) => Promise<any>,
            contractInfo: (num: string) => Promise<any>
        };
        switch (systemType) {
            case "cashloan":
                serviceApi = losClApi;
                break;
            case "united":
                serviceApi = losUnitedApi;
                break;
            case "merchant":
                serviceApi = losMcApi;
                break;
        }
        let resultContractInfo = await serviceApi.getDocResubmit(contractNumber);
        let bundleList = resultContractInfo?.data ?? [];
        let contractInfo = await serviceApi.contractInfo(contractNumber);
        let dataInfo = {
            customerName: contractInfo?.data?.customerName,
            phoneNumber: contractInfo?.data?.phoneNumber,
            idCardNumber: contractInfo?.data?.idCardNumber,
        }
        // for(let bundle of bundleList) {
        //     let docType = bundle && bundle.docType ? bundle.docType : '';
        //     let docObj;
        //     if (docType != '') docObj = await serviceApi.docInfo(docType);
        //     bundle.docName = docObj && docObj.data && docObj.data.valueNameVn ? docObj.data.valueNameVn : ''; 

        // }
        // let listBundleDocs = [];

        // for(let bundle of bundleList) {
        //     let objBundle = {
        //         minDocs: 1,
        //         maxDocs: 1,
        //         docList: []
        //     };
        //     objBundle.docList.push(bundle);
        //     let docTypeBundle = bundle && bundle.docType ? bundle.docType : '';
        //     if (docTypeBundle != '') listBundleDocs.push(objBundle);
        //     // let docTypeBundle = bundle && bundle.docType ? bundle.docType : '';
        //     // let obj = listBundleDocs.find(i => i && i.docType && i.docType == docTypeBundle);
        //     // if(!obj) listBundleDocs.push(bundle);
        // }
        let listDocs = bundleList;
        if (status == 'NEW') {
            bundleList = [];
            listDocs = await contractService.getListDocs(contractNumber);
            listDocs = listDocs && listDocs.response ? listDocs.response : '[]';
            if (listDocs && listDocs != '') {
                listDocs = JSON.parse(listDocs);
            }
        }
        for (let bundleObj of listDocs) {
            let listDocBunble = bundleObj && bundleObj.docList && bundleObj.docList.length ? bundleObj.docList : [];
            let docsNonDup = [];
            let isResubmit = 0;
            for (let docObj of listDocBunble) {
                let docTypeBundle = docObj && docObj.docType ? docObj.docType : '';
                let docInfo = await masterDataApi.docInfo(docTypeBundle);
                let docTemp = docInfo && docInfo.data ? docInfo.data : {}
                docObj.docName = docTemp.valueNameVn || docTemp.valueNameEn || docTypeBundle;
                let obj = docsNonDup.find(i => i && i.docType && i.docType == docTypeBundle);
                if (!obj) docsNonDup.push(docObj);
                if (docObj && docObj.isResubmit && docObj.isResubmit == 1) isResubmit = 1;
            }
            if (isResubmit == 1) for (let i of docsNonDup) i.isResubmit = 1;
            bundleObj.docList = docsNonDup;
        }
        bundleList = listDocs;
        // let files = [];
        let data = {
            dataInfo: dataInfo,
            listDocs: bundleList
        }
        response.status(200).json({ data: data })
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('list docs error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }

});

export default router;