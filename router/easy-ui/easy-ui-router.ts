import { Router } from "express";
import { selfieImageRepo } from "../../repositories";

const router = Router();

router.get("/getDocumentWithoutCheckDocs", async (req, res, next) => {
    let output = [];
    try {
        let contractNumber = req.query.contractNumber as string;
        let selfies = await selfieImageRepo.getSelfieByContract(contractNumber);
        output = selfies?.map(x => ({
            url: null,
            docType: "PIC_ESIGN",
            docName: "PIC_ESIGN",
            docId: x.image_id,
            docDesc: "ẢNH CHỤP KHUÔN MẶT LÚC KÝ",
            fileName: x.image_id.replace("esigning/selfie/", ""),
            docSource: ""
        }));
    } catch (error) {
    }
    res.json({ code: 0, message: 'Success', data: output });
});

export default router;