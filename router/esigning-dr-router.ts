import express, { Request, Response } from 'express';
import * as configService from '../services/config-service';
import * as esigningService from '../services/esigning-service';
import * as smsService from '../services/sms-service';
import * as common from "../utils/common";
import * as emailService from "../services/email-service";
import { ContractDbrInfo, SmsType } from '../types';
import { getDbrContractFiles, getDbrContractInfo } from '../utils/contract-utils';
import { aaaApi } from '../api';
import * as fileService from "../services/file-service";

const router = express.Router();

router.post('/customer-register', async (request, response) => {
    try {
        // let data = null;
        let status: string;
        let msg: string;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return response.status(400).json(baseResponse);
        }
        let contractAnnexNumber = contractNumber + "_DBR";
        let contractScheduleNumber = contractNumber + "_LTT";
        // check otp resend > 5
        let checkLockContract = await esigningService.getLockContract(contractAnnexNumber);
        let cloudConfig = configService.getConfig();
        let baseResponseOTP = {
            status: 'FAILED_REQUEST',
            msg: 'Exceeded the maximum number of requests',
            data: {
                timeLock: cloudConfig.data.fptEsignApi.timeLock
            }
        }
        if (checkLockContract) {
            return response.status(400).json(baseResponseOTP);
        } else {
            let exceededMaximum = await esigningService.findOptSend(contractAnnexNumber, contractAnnexNumber);
            if (exceededMaximum) {
                return response.status(400).json(baseResponseOTP);
            }
        }
        let skipSms = false;
        let fileResponse = await getDbrContractFiles(contractNumber);
        let infoResponse = await getDbrContractInfo(contractNumber);
        let contractAnnexInfo = infoResponse?.data;
        if (fileResponse.code != 0 || !contractAnnexInfo) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex is not found',
            }
            return response.status(400).json(baseResponse);
        } else {
            let contractSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
            if (contractSigned) {
                let baseResponse = {
                    status: 'fail',
                    msg: 'contract annex signed already',
                }
                return response.status(400).json(baseResponse);
            }
            // sign annex
            let fileData2 = fileResponse.data.base64GenContractDebt;
            let result2 = await prepareCertificate(fileData2, contractAnnexNumber, contractAnnexInfo, "dbr_contract");
            status = result2.status;
            msg = result2.msg;
            skipSms = true;

            // sign schedule
            let fileData = fileResponse.data.base64GenPaymentSchedule;
            let result = await prepareCertificate(fileData, contractScheduleNumber, contractAnnexInfo, "dbr_schedule", skipSms);
            status = result.status;
            msg = result.msg;
        }

        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: contractNumber,
                authorizeCodeExpireTime: 300
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
    }
});

/**
 * @param skipSms Bỏ qua gửi OTP lịch thanh toán nếu đã có OTP phụ lục
 */
async function prepareCertificate(fileData: string, token: string, contractAnnexInfo: ContractDbrInfo, smsType: SmsType, skipSms: boolean = false) {
    let status: string;
    let msg: string;
    let prepareCertificateInfo = await esigningService.findPrepareCertificateByContractNumber(token);
    if (prepareCertificateInfo.rowCount === 0 || prepareCertificateInfo.rows[0].response_code != 0) {
        let resultRegister = await esigningService.customerRegisterDr(contractAnnexInfo, fileData, token);
        if (resultRegister.status === 'success' && !skipSms) {
            smsService.sendSmsOtpToSign(contractAnnexInfo.phone_number, resultRegister.authorizeCode, token, smsType);
        }
        status = resultRegister.status;
        msg = resultRegister.msg;
    } else {
        let agreementUUIDCreatedDate = prepareCertificateInfo.rows[0].agreement_uuid_created_date;
        let isAgreementUUIDExipred = esigningService.isAgreementUUIDExipred(agreementUUIDCreatedDate);
        common.log('isAgreementUUIDExipred: ' + isAgreementUUIDExipred);
        if (isAgreementUUIDExipred === true) {
            let resultRegister = await esigningService.customerRegisterDr(contractAnnexInfo, fileData, token);
            if (resultRegister.status === 'success' && !skipSms) {
                smsService.sendSmsOtpToSign(contractAnnexInfo.phone_number, resultRegister.authorizeCode, token, smsType);
            }
            status = resultRegister.status;
            msg = resultRegister.msg;
        } else {
            //check prepare file success
            let result = await esigningService.findPrepareFileSuccessByAgreementUUID(prepareCertificateInfo.rows[0].current_agreement_uuid);
            if (result.rowCount >= 1) {
                let resultRegenerateAuthorizeCode = await esigningService.regenerateAuthorizationCode(prepareCertificateInfo.rows[0].current_agreement_uuid, token);
                if (resultRegenerateAuthorizeCode.status === 'success' && !skipSms) {
                    smsService.sendSmsOtpToSign(contractAnnexInfo.phone_number, resultRegenerateAuthorizeCode.authorizeCode, token, smsType);
                }
                status = resultRegenerateAuthorizeCode.status;
                msg = resultRegenerateAuthorizeCode.msg;
            } else {
                let resultPrepareFile = await esigningService.prepareFile(prepareCertificateInfo.rows[0].current_agreement_uuid, token, fileData);
                common.log(resultPrepareFile);
                if (resultPrepareFile.status === 'success' && !skipSms) {
                    smsService.sendSmsOtpToSign(contractAnnexInfo.phone_number, resultPrepareFile.authorizeCode, token, smsType);
                }
                status = resultPrepareFile.status;
                msg = resultPrepareFile.msg;
            }
            status = 'success';
            msg = 'register success';
        }
    }
    return { msg, status };
}

router.post('/customer-sign', async function (request, response) {
    try {
        let status: string;
        let msg: string;
        // let data = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let authorizeCode = request.body.authorizeCode as string;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return response.status(400).json(baseResponse);
        }
        let infoResponse = await getDbrContractInfo(contractNumber);
        let contractAnnexInfo = infoResponse?.data;
        if (!contractAnnexInfo) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex is not found',
            }
            return response.status(400).json(baseResponse);
        }
        let contractAnnexNumber = contractNumber + "_DBR";
        let contractScheduleNumber = contractNumber + "_LTT";
        let contractSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
        if (contractSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex signed already',
            }
            return response.status(400).json(baseResponse);
        }
        let checkContractRegisterResult = await esigningService.isContractRegistered(contractAnnexNumber);
        common.log(checkContractRegisterResult);
        if (checkContractRegisterResult.status === 'success') {
            // Sign annex
            let signResult = await esigningService.customerSignDr(contractAnnexNumber, checkContractRegisterResult.data.agreementUUID, authorizeCode, checkContractRegisterResult.data.billCode);
            status = signResult.status;
            msg = signResult.msg;

            if (status !== "fail") {
                let checkScheduleRegisterResult = await esigningService.isContractRegistered(contractScheduleNumber);
                // get OTP for schedule
                authorizeCode = (await esigningService.findOpt(contractScheduleNumber))?.authorize_code;
                common.log(checkScheduleRegisterResult);
                if (checkScheduleRegisterResult.status === 'success') {
                    // Sign schedule
                    let signResult = await esigningService.customerSignDr(contractScheduleNumber, checkScheduleRegisterResult.data.agreementUUID, authorizeCode, checkScheduleRegisterResult.data.billCode);
                    status = signResult.status;
                    msg = signResult.msg;
                } else {
                    status = checkContractRegisterResult.status;
                    msg = checkContractRegisterResult.msg;
                }
            }
        } else {
            status = checkContractRegisterResult.status;
            msg = checkContractRegisterResult.msg;
        }
        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: contractNumber
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post('/contract/check', async function (request, response) {
    try {
        let status: string = null;
        let msg: string = null;

        let contractNumber = request.body.contractNumber as string;
        let identityCard = request.body.identityCard as string;
        if (!contractNumber || !identityCard) {
            return response.status(400).json({
                status: 'fail',
                msg: "invalid input"
            });
        }
        let infoResponse = await getDbrContractInfo(contractNumber);
        if (infoResponse.code != 1 || infoResponse.data?.identity_card !== identityCard) {
            return response.status(400).json({
                status: 'fail',
                msg: "contract annex is not found"
            });
        }
        const contractAnnexNumber = contractNumber + "_DBR";
        let contractSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
        if (contractSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex signed already',
            }
            return response.status(400).json(baseResponse);
        }

        let token = await aaaApi.getToken(contractNumber, identityCard);
        let data = { contractNumber, token };
        common.log('response contract:');
        common.log(data);
        let baseResponse = {
            status: status,
            msg: msg,
            data: data
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
    }
});

/**
 * Lấy file phụ lục, lịch thanh toán chưa kí
 */
router.post('/contract/file', async (request: Request, response: Response) => {
    try {
        let status = null;
        let msg = null;
        let data = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return response.status(400).json(baseResponse);
        }
        let infoResponse = await getDbrContractInfo(contractNumber);
        if (infoResponse.code != 1) {
            return response.status(400).json({
                status: 'fail',
                msg: "contract annex is not found"
            });
        }
        const contractAnnexNumber = contractNumber + "_DBR";
        let contractSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
        if (contractSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex signed already',
            }
            return response.status(400).json(baseResponse);
        }

        let fileResponse = await getDbrContractFiles(contractNumber);
        if (fileResponse.code == 0) {
            data = {
                fileName: "DBR" + contractNumber + '.pdf',
                fileData: fileResponse.data.base64GenContractDebt,
                scheduleFileData: fileResponse.data.base64GenPaymentSchedule,
                contractNumber: contractNumber
            }
        } else {
            status = 'fail';
            msg = 'contract annex is not found';
        }

        let baseResponse = {
            status: status,
            msg: msg,
            data: data
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('get contract annex file error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post('/contract/send-email', async function (request, response) {
    let status: string;
    let msg: string;
    let token = request.headers?.token as string;
    let contractNumber = request.body.contractNumber as string;
    let validateToken = await aaaApi.validateToken(token, contractNumber);
    if (!validateToken) {
        let baseResponse = {
            status: 'invalid token',
            msg: 'invalid token',
        }
        return response.status(400).json(baseResponse);
    }
    let infoResponse = await getDbrContractInfo(contractNumber);
    if (infoResponse.code != 1) {
        return response.status(400).json({
            status: 'fail',
            msg: "contract annex is not found"
        });
    }
    let contractAnnexNumber = contractNumber + "_DBR";
    let contractScheduleNumber = contractNumber + "_LTT";
    let annexSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
    let scheduleSigned = await esigningService.contractNumberSigned(contractScheduleNumber);
    if (annexSigned || scheduleSigned) {
        let annexSignedEntity = annexSigned && await esigningService.findFileSignedData(annexSigned.agreement_uuid, contractAnnexNumber);
        let scheduleSignedEntity = scheduleSigned && await esigningService.findFileSignedData(scheduleSigned.agreement_uuid, contractScheduleNumber);
        if (annexSignedEntity || scheduleSignedEntity) {
            let receiver = request.body.emailAddress;
            let subject = 'EASY CREDIT - PHỤ LỤC HỢP ĐỒNG TÍN DỤNG ĐIỆN TỬ';
            let content = 'Đính kèm theo email này là Phụ lục hợp đồng tín dụng điện tử cho khoản vay của Quý khách tại EASY CREDIT. Quý khách có thể xem Điều khoản và Điều kiện chung (đính kèm và là một bộ phận không thể tách rời của Hợp đồng tín dụng điện tử) theo đường link: https://easycredit.vn/dieu-khoan-va-dieu-kien-giao-dich-chung';
            let files = [];
            for (const item of [annexSignedEntity, scheduleSignedEntity]) {
                if (item) {
                    files.push({
                        fileName: item.file_name,
                        content: common.arrayBufferToBase64(item.s3_key ? await fileService.downloadFile(item.s3_key) : Buffer.from(item.signed_file_data, "base64")),
                    })
                }
            }
            let result = await emailService.sendEmailContractAnnexFile(receiver, subject, content, files);
            if (!result) {
                let baseResponse = {
                    status: 'fail',
                    msg: 'failed to send email',
                }
                return response.status(400).json(baseResponse);
            }
            status = 'success';
            msg = 'sent email success';
        } else {
            status = 'fail';
            msg = 'contract annex is not found';
        }

    } else {
        status = 'fail';
        msg = 'contract annex is not signed'
    }

    let baseResponse = {
        status: status,
        msg: msg,
    }

    response.json(baseResponse);
});

// TODO: web không dùng
router.post('/not-receive-otp', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let contractNumber = request.body.contractNumber as string;
        let infoResponse = await getDbrContractInfo(contractNumber);
        if (infoResponse.code != 1) {
            return response.status(400).json({
                status: 'fail',
                msg: "contract annex is not found"
            });
        }
        const contractAnnexNumber = contractNumber + "_DBR";
        let contractSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
        if (contractSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex signed already',
            }
            return response.status(400).json(baseResponse);
        }
        let contractToken: string = contractAnnexNumber;
        let checkContractRegisterResult = await esigningService.isContractRegistered(contractToken);
        if (checkContractRegisterResult.status === 'success') {
            let result = await esigningService.findAuthorizationCodeInfoByAgreementUUID(checkContractRegisterResult.data.agreementUUID);
            let currentDate = new Date();
            if (result?.response_code == 0 && currentDate.getTime() < result.expire_date.getTime()) {
                status = 'success';
                msg = 'otp is valid';
                smsService.sendSmsOtpToSign(checkContractRegisterResult.data.phoneNumber, result.authorize_code, contractToken, "dbr_contract");
            } else {
                status = 'fail';
                msg = 'otp is expired';
            }
        } else {
            status = checkContractRegisterResult.status;
            msg = checkContractRegisterResult.msg;
        }

        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: contractNumber
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post('/resend-otp', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return response.status(400).json(baseResponse);
        }
        let infoResponse = await getDbrContractInfo(contractNumber);
        if (infoResponse.code != 1) {
            return response.status(400).json({
                status: 'fail',
                msg: "contract annex is not found"
            });
        }
        const contractAnnexNumber = contractNumber + "_DBR";
        const contractScheduleNumber = contractNumber + "_LTT";
        let contractAnnexSigned = await esigningService.contractNumberSigned(contractAnnexNumber);
        let contractScheduleSigned = await esigningService.contractNumberSigned(contractScheduleNumber);
        if (contractAnnexSigned && contractScheduleSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'contract annex signed already',
            }
            return response.status(400).json(baseResponse);
        }
        if (!contractAnnexSigned) {
            // Tạo OTP cho phụ lục + lịch thanh toán, chỉ gửi OTP phụ lục cho KH
            let result = await smsService.resendOtp(contractAnnexNumber, "dbr_contract");
            if (result.response) {
                return response.status(400).json(result.response);
            } else if (result.status === "success") {
                result = await smsService.resendOtp(contractScheduleNumber, "dbr_contract", true);
                if (result.response) {
                    return response.status(400).json(result.response);
                }
            }
            status = result.status;
            msg = result.msg;
        } else {
            // Tạo OTP lịch thanh toán
            let result = await smsService.resendOtp(contractScheduleNumber, "dbr_schedule", true);
            if (result.response) {
                return response.status(400).json(result.response);
            }
            status = result.status;
            msg = result.msg;
        }
        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: contractNumber,
                authorizeCodeExpireTime: 300
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Internal server error',
        }
        console.error('check contract error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }
});

export default router;