import express, { Request, Response } from "express";
import * as documentService from "../services/document-service";
import * as common from "../utils/common";
import * as fileDataSignedRepo from "../repositories/file-data-signed-repo";
import { getContractSystemType } from "../utils/contract-utils";
import * as contractService from '../services/contract-service';
import { losApi, losClApi, losMcApi, losUnitedApi, losVfApi } from "../api";
import * as fileService from "../services/file-service";
import { ResponseUpload } from "../types";
import { abTestingService, esigningService, faceService } from "../services";

const router = express.Router();

router.get("/contract/annexes/:id", async (req: Request, res: Response) => {
    let contractNumber = req.params.id as string;
    let signedData = await documentService.getSignedAnnexFile(contractNumber);
    if (signedData) {
        res.writeHead(200);
        let buffer = Buffer.from(signedData.toString(), "base64");
        res.end(buffer);
    } else {
        res.status(400).send({ status: "fail", msg: "File not found." });
    }
});
router.get("/contract/ltt/:id", async (req: Request, res: Response) => {
    let contractNumber = req.params.id as string;
    let signedData = await documentService.getSignedScheduleFile(contractNumber);
    if (signedData) {
        res.writeHead(200);
        let buffer = Buffer.from(signedData.toString(), "base64");
        res.end(buffer);
    } else {
        res.status(400).send({ status: "fail", msg: "File not found." });
    }
});
router.get("/contract/:id", async (req: Request, res: Response) => {
    let contractNumber = req.params.id as string;
    let buffer: Buffer;
    let s3Key = await fileDataSignedRepo.findS3ByContractNumber(contractNumber);
    if (s3Key) {
        common.log(s3Key);
        buffer = await fileService.downloadFile(s3Key);
    } else {
        let signedData = (await fileDataSignedRepo.findByContractNumber(contractNumber));
        buffer = Buffer.from(signedData.signed_file_data.toString(), "base64");
    }
    if (buffer) {
        res.writeHead(200, { 'Content-Type': 'application/pdf', 'Content-Disposition': "attachment; filename=" + `${contractNumber}.pdf` });
        res.end(buffer);
    } else {
        res.status(400).send({ status: "fail", msg: "File not found." });
    }
});
router.post("/contract/upload-s3", async (req: Request, res: Response) => {
    let contractNumber = req.body.contractNumber;
    let data = await fileDataSignedRepo.findByContractNumber(contractNumber);
    if (data && !data.s3_key) {
        try {
            const signedData = data.signed_file_data.toString();
            var s3Key = `signed-files/${contractNumber}.pdf`;
            await fileService.uploadFile(s3Key, Buffer.from(signedData, "base64"));
            await fileDataSignedRepo.updateS3Info(contractNumber, s3Key);
            return res.send({ code: 0, message: "success" });
        } catch {
            return res.send({ code: 1, message: "reupload failed" })
        }
    }

})
router.post("/contract/reupload", async (req: Request, res: Response) => {
    let contractNumber = req.body.contractNumber;
    let signedData = await documentService.getSignedContractFile(contractNumber);
    if (signedData) {
        try {
            const systemType = getContractSystemType(contractNumber);
            let responseUpload: ResponseUpload;
            if (systemType === "credit") {
                responseUpload = await losApi.uploadSignedFileContract(contractNumber, signedData);
            } else if (systemType === "merchant") {
                responseUpload = await losMcApi.uploadSignedFileContract(contractNumber, signedData);
            } else if (systemType === "vinfast") {
                responseUpload = await losVfApi.uploadSignedFileContract(contractNumber, signedData);
            } else if (systemType === "cashloan") {
                responseUpload = await losClApi.uploadSignedFileContract(contractNumber, signedData);
            } else if (systemType === "united") {
                responseUpload = await losUnitedApi.uploadSignedFileContract(contractNumber, signedData);
            }

            if (responseUpload?.data?.bundleList?.length) {
                let bundleList = responseUpload.data.bundleList;
                let stringFile = JSON.stringify(bundleList)
                await contractService.saveListDocs(contractNumber, stringFile, 1);
            }

            return res.send({ code: 0, message: "success" });
        } catch {
            return res.send({ code: 1, message: "reupload failed" })
        }
    }
});

router.post("/selfie/setting", async (req: Request, res: Response) => {
    let partners = req.body.partners;
    try {
        await faceService.updatePartners(partners);
        await faceService.reloadConfig();

        return res.send({ code: 0, message: "success" });
    } catch (error) {
        common.error(error);
        return res.send({ code: 1, message: "update failed" })
    }
});

router.post("/ab-testing/configs", async (req: Request, res: Response) => {
    let configs = req.body.configs;
    try {
        await abTestingService.updateConfigs(configs);
        await abTestingService.reloadConfig();

        return res.send({ code: 0, message: "success" });
    } catch (error) {
        common.error(error);
        return res.send({ code: 1, message: "update failed" })
    }
});

router.post("/selfie/manual", async (req: Request, res: Response) => {
    let id = req.body.id as number;
    let status = req.body.status as string;

    try {
        await faceService.manualPass(id, status);

        return res.send({ code: 0, message: "success" });
    } catch (error) {
        common.error(error);
        return res.send({ code: 1, message: "update failed" })
    }
})

router.get("/files/*", async (req: Request, res: Response) => {
    try {
        let s3Key = req.params?.[0];
        if (s3Key) {
            let buffer = await fileService.downloadFile(s3Key);
            if (buffer) {
                let index = s3Key.lastIndexOf('.');
                if (index >= 0) {
                    let extension = s3Key.substring(index + 1);
                    if (extension === "pdf") {
                        res.set('Content-Type', 'application/pdf');
                    } else if (['jpg', 'jpeg', 'png'].includes(extension)) {
                        res.set("Content-Type", "image/" + extension);
                    }
                }
                res.writeHead(200);
                res.end(buffer);
                return;
            }
        }
        return res.json({ code: 1, message: "file not found" });
    } catch (error) {
        common.error(error);
        return res.send({ code: 1, message: "server error" })
    }
});

router.post('/customer-sign-ec', async (req, res) => {
    let contractNumber = req.body.contractNumber;
    let signedData = await documentService.getSignedContractFile(contractNumber + "_step1");
    let contractInfo = await contractService.findContractInfoByContractNumber(contractNumber);
    if (signedData) {
        let result = await esigningService.signEvfOnly(signedData.toString("base64"), contractNumber, contractInfo?.partner_code);
        if (contractNumber.startsWith('2')) {
            if (result.data) {
                await losMcApi.uploadSignedFileContract(contractNumber, Buffer.from(result.data, "base64"));
            }
            return res.status(result.code == 0 ? 200 : 400).send({ code: result.code, msg: result.msg });
        } else {
            return res.status(result.code == 0 ? 200 : 400).send(result);
        }
    }
    return res.status(400).json({
        status: 'fail',
        msg: 'signing failed'
    });
});

export default router;