import { Router, Request } from "express";
import { esigningService, fileService } from "../../services";

const router = Router();

const types = ["BCTD", "LD"];

router.post("/sign", async (req, res) => {
    let { contractNumber, filePath, type } = req.body;
    let contractFile = Array.isArray(req.files?.file) ? req.files.file[0] : req.files?.file;
    
    if (!contractNumber || (!contractFile && !filePath)) {
        return res.status(400).send({
            code: 1,
            msg: 'Invalid input'
        });
    }
    if (!types.includes(type)) {
        return res.status(400).send({
            code: 1,
            msg: 'Invalid type'
        });
    }
    try {
        let fileData: string;
        if (contractFile) {
            fileData = contractFile.data.toString("base64");
        } else {
            fileData = (await fileService.downloadLosFile(filePath)).toString("base64");
        }
        let result = await esigningService.signEvfOnly(fileData, `${contractNumber}_${type}`, `VUIP_${type}`);
        return res.status(result.code == 0 ? 200 : 400).send(result);
    } catch (error) {
        res.status(500).send({ code: -1, msg: 'Internal server error' });
    }
});

export default router;