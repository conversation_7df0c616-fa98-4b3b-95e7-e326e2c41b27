import { Router, Request } from "express";
import { losMcApi } from "../../api";
import { esigningService, fileService } from "../../services";
import * as common from "../../utils/common";

const router = Router();

router.post("/check-signature", async (req, res) => {
    try {
        let file = Array.isArray(req.files.file) ? req.files.file[0] : req.files.file;
        if (!file) {
            return res.status(400).send({
                code: 1,
                msg: 'Invalid input'
            });
        }
        let result = await esigningService.checkSignature(file.data);
        if (result) {
            res.send({ code: 0, msg: "OK", data: result });
        } else {
            res.status(400).send({ code: 1, msg: "No signature found" });
        }
    } catch (error) {
        common.error(error);
        res.status(500).send({ code: -1, msg: "Internal server error" });
    }
});

router.post("/sign", async (req, res) => {
    let { contractNumber, filePath } = req.body;
    let contractFile = Array.isArray(req.files?.file) ? req.files.file[0] : req.files?.file;

    if (!contractNumber || (!contractFile && !filePath)) {
        return res.status(400).send({
            code: 1,
            msg: 'Invalid input'
        });
    }
    try {
        let fileData: string;
        if (contractFile) {
            fileData = contractFile.data.toString("base64");
        } else {
            fileData = (await fileService.downloadLosFile(filePath)).toString("base64");
        }
        let result = await esigningService.signEvfOnly(fileData, contractNumber, "MISA");
        if (result.data) {
            await losMcApi.uploadSignedFileContract(contractNumber, Buffer.from(result.data, "base64"));
        }
        return res.status(result.code == 0 ? 200 : 400).send({ code: result.code, msg: result.msg });
    } catch (error) {
        res.status(500).send({ code: -1, msg: 'Internal server error' });
    }
});

router.post("/sign-statement", async (req, res) => {
    let { contractNumber, filePath } = req.body;
    let contractFile = Array.isArray(req.files?.file) ? req.files.file[0] : req.files?.file;

    if (!contractNumber || (!contractFile && !filePath)) {
        return res.status(400).send({
            code: 1,
            msg: 'Invalid input'
        });
    }
    try {
        let fileData: string;
        if (contractFile) {
            fileData = contractFile.data.toString("base64");
        } else {
            fileData = (await fileService.downloadLosFile(filePath)).toString("base64");
        }
        let result = await esigningService.signEvfOnly(fileData, `${contractNumber}_SK`, "MISA_SK");
        return res.status(result.code == 0 ? 200 : 400).send(result);
    } catch (error) {
        res.status(500).send({ code: -1, msg: 'Internal server error' });
    }
});

router.post("/sign-contract", async (req: Request & { disableLog: boolean }, res) => {
    let { contractNumber, filePath, type } = req.body;
    let contractFile = Array.isArray(req.files?.file) ? req.files.file[0] : req.files?.file;

    if (!contractNumber || (!contractFile && !filePath)) {
        return res.status(400).send({
            code: 1,
            msg: 'Invalid input'
        });
    }
    try {
        type ??= 'HDHM';
        req.disableLog = true;
        let fileData: string;
        if (contractFile) {
            fileData = contractFile.data.toString("base64");
        } else {
            fileData = (await fileService.downloadLosFile(filePath)).toString("base64");
        }
        let result = await esigningService.signEvfOnly(fileData, `${contractNumber}_${type}`, `MISA_${type}`);
        return res.status(result.code == 0 ? 200 : 400).send(result);
    } catch (error) {
        res.status(500).send({ code: -1, msg: 'Internal server error' });
    }
});

export default router;