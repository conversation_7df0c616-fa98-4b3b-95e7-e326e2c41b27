import express, { Request, Response } from 'express';
const router = express.Router();
import * as common from "../utils/common";
import * as configService from '../services/config-service';
import * as esigningService from '../services/esigning-service';
import * as smsService from '../services/sms-service';
import * as contractService from '../services/contract-service';
import { callbackUpdateStatusLos, cancelLoanContract, DECISION_V02_RES_DECISION, getContractFile, getContractInfo, getContractSystemType, verifyLoan } from '../utils/contract-utils';
import { aaaApi, deApi, googleApi } from '../api';
import { contractProcessingRepo, selfieImageRepo } from '../repositories';
import { ResponseUploadData, SelfieImageEntity } from '../types';
import { documentService, faceService, fileService } from '../services';
import crypto from "crypto";
import requestIp from "request-ip";
import { ManagedUpload } from 'aws-sdk/clients/s3';

const skipEmailPartners = [
    "DNSE",
    "GIMO",
    "FUND",
    "MANF",
    "VUIP"
];
const signCustomerOnlyPartners = [
    "FUND_WD",
    "FINV_WD"
];

router.post('/customer-register', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let identityCard = request.body.identityCard as string;
        let newFileHost = request.body.newFileHost as boolean;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'Token không hợp lệ',
                });
            }
        }
        if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
            common.log("Face image is not verified");
            let baseResponse = {
                status: 'face not passed',
                msg: 'face not passed',
            }
            return response.status(400).json(baseResponse);
        }
        // check otp resend > 5
        let checkLockContract = await esigningService.getLockContract(contractNumber);
        let cloudConfig = configService.getConfig();
        let timeLock = cloudConfig.data.fptEsignApi.timeLock;
        let baseResponseOTP = {
            status: 'FAILED_REQUEST',
            msg: 'Quá số lần gửi OTP',
            data: {
                timeLock: timeLock
            }
        }
        if (checkLockContract) {
            return response.status(400).json(baseResponseOTP);
        } else {
            let exceededMaximum = await esigningService.findOptSend(contractNumber, contractNumber);
            if (exceededMaximum) {
                return response.status(400).json(baseResponseOTP);
            }
        }

        let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
        if (resultContractNumberSigned && !process.env.DEBUG) {
            let baseResponse = {
                status: 'fail',
                msg: 'Hợp đồng đã được ký',
            }
            return response.status(400).json(baseResponse);
        }
        let contractDetailResponse = await getContractFile(contractNumber, [], newFileHost);
        if (contractDetailResponse) {
            // require('fs').writeFileSync(`${contractNumber}.pdf`, Buffer.from(contractDetailResponse, "base64"));
            //insert agreed terms
            let contractInfo = await contractService.findContractInfoByContractNumber(contractNumber);
            if (!contractInfo) {
                common.log("Error: Contract not found in db");
                let baseResponse = {
                    status: 'fail',
                    msg: 'Không tìm thấy hợp đồng',
                }
                return response.status(400).json(baseResponse);
            }

            let termsAndConditionsList = await contractService.getListTermsConditionsAgreed(contractNumber);
            if (termsAndConditionsList.length <= 0) {
                await contractService.insertTermsConditionsAgreed(contractNumber, request.body.termsAndConditions);
            }

            let resultRegister = await esigningService.customerRegister(contractInfo, contractDetailResponse);
            if (resultRegister.status === 'success') {
                smsService.sendSmsOtpToSign(contractInfo.phone_number, resultRegister.authorizeCode, contractNumber, "contract");
            }
            status = resultRegister.status;
            msg = resultRegister.msg;
            // } else {
            //     let agreementUUIDCreatedDate = prepareCertificateInfo.rows[0].agreement_uuid_created_date;
            //     let isAgreementUUIDExipred = esigningService.isAgreementUUIDExipred(agreementUUIDCreatedDate);
            //     common.log('isAgreementUUIDExipred: ' + isAgreementUUIDExipred);
            //     if (isAgreementUUIDExipred === true) {
            //         let resultRegister = await esigningService.customerRegister(contractInfo, contractDetailResponse);
            //         if (resultRegister.status === 'success') {
            //             smsService.sendSmsOtpToSign(contractInfo.phone_number, resultRegister.authorizeCode, contractNumber, "contract");
            //         }
            //         status = resultRegister.status;
            //         msg = resultRegister.msg;
            //     } else {
            //         //check prepare file success
            //         let result = await esigningService.findPrepareFileSuccessByAgreementUUID(prepareCertificateInfo.rows[0].current_agreement_uuid);
            //         if (result.rowCount >= 1) {
            //             let resultRegenerateAuthorizeCode = await esigningService.regenerateAuthorizationCode(prepareCertificateInfo.rows[0].current_agreement_uuid, contractNumber);
            //             if (resultRegenerateAuthorizeCode.status === 'success') {
            //                 smsService.sendSmsOtpToSign(contractInfo.phone_number, resultRegenerateAuthorizeCode.authorizeCode, contractNumber, "contract");
            //             }
            //             status = resultRegenerateAuthorizeCode.status;
            //             msg = resultRegenerateAuthorizeCode.msg;
            //         } else {
            //             let resultPrepareFile = await esigningService.prepareFile(prepareCertificateInfo.rows[0].current_agreement_uuid, contractInfo.contract_number, contractDetailResponse);
            //             common.log(resultPrepareFile);
            //             if (resultPrepareFile.status === 'success') {
            //                 smsService.sendSmsOtpToSign(contractInfo.phone_number, resultPrepareFile.authorizeCode, contractNumber, "contract");
            //             }
            //             status = resultPrepareFile.status;
            //             msg = resultPrepareFile.msg;
            //         }
            //         status = 'success';
            //         msg = 'register success';
            //     }
            // }

        } else {
            status = 'fail';
            msg = 'Không tìm thấy hợp đồng';
        }
        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: contractNumber,
                authorizeCodeExpireTime: 300
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Lỗi máy chủ',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post('/customer-sign', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let responseUpload: ResponseUploadData = {};
        // let data = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber;
        let authorizeCode = request.body.authorizeCode;
        let identityCard = request.body.identityCard;
        let asynchronous = !!request.body.asynchronous;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'Token không hợp lệ',
                });
            }
        }
        if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
            common.log("Face image is not verified");
            let baseResponse = {
                status: 'face not passed',
                msg: 'face not passed',
            }
            return response.status(400).json(baseResponse);
        }
        if (asynchronous) {
            let contractProcessing = await contractProcessingRepo.getContractProcessing(contractNumber);
            if (contractProcessing) {
                if (contractProcessing.message) {
                    response.status(200).json({
                        status: contractProcessing.status,
                        msg: contractProcessing.message,
                        responseUpload: JSON.parse(contractProcessing.response_upload)
                    });
                    setTimeout(() => {
                        contractProcessingRepo.deleteProcessing(contractProcessing.id);
                    }, 10000);
                    return;
                } else {
                    return response.status(200).json({
                        status: 'signing',
                        msg: 'Hợp đồng đang được ký'
                    });
                }
            }
        }
        let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
        if (resultContractNumberSigned && !process.env.DEBUG) {
            let baseResponse = {
                status: 'fail',
                msg: 'Hợp đồng đã được ký',
            }
            return response.status(400).json(baseResponse);
        }
        if (asynchronous)
            await contractProcessingRepo.insertProcessing(contractNumber);

        let [checkResult, resultVerifyLoan] = await Promise.all([
            getContractInfo(contractNumber),
            verifyLoan(contractNumber)
        ]);
        if (resultVerifyLoan && !resultVerifyLoan?.data?.isVerifyLoan) {
            if (resultVerifyLoan?.data?.decision && resultVerifyLoan?.data?.decision != DECISION_V02_RES_DECISION.WAIT_CIC) {
                await callbackUpdateStatusLos(contractNumber, resultVerifyLoan?.data?.decision)
            }
            return response.status(400).json({
                status: 'fail',
                msg: resultVerifyLoan.data?.messageVerifyLoan,
            });
        }
        let contractInfo = await contractService.findContractInfoByContractNumber(contractNumber);
        if (checkResult.code === 'SUCCESS') {
            let checkContractRegisterResult = await esigningService.isContractRegistered(contractNumber);
            common.log(checkContractRegisterResult);
            if (checkContractRegisterResult.status === 'success') {
                // bypass otp trên uat
                if (process.env.NODE_ENV === 'uat' && authorizeCode === '999999') {
                    authorizeCode = (await esigningService.findOpt(contractNumber))?.authorize_code ?? authorizeCode;
                }

                let signResult = await esigningService.customerSign(contractNumber, checkContractRegisterResult.data.agreementUUID, authorizeCode, checkContractRegisterResult.data.billCode, { signEC: !signCustomerOnlyPartners.includes(contractInfo?.partner_code) });
                status = signResult.status;
                msg = signResult.msg;
                responseUpload = signResult.responseUpload?.data;
            } else {
                status = checkContractRegisterResult.status;
                msg = checkContractRegisterResult.msg;
            }
        } else {
            status = 'fail';
            msg = 'Không tìm thấy hợp đồng';
        }
        if (responseUpload?.bundleList?.length) {
            let bundleList = responseUpload.bundleList;
            let stringFile = JSON.stringify(bundleList)
            await contractService.saveListDocs(contractNumber, stringFile, 1);
            responseUpload.bundleList = [];
        }
        let baseResponse = {
            status: status,
            msg: msg,
            responseUpload: responseUpload,
            data: {
                contractNumber: contractNumber
            },
            skipEmail: skipEmailPartners.find(x => contractInfo?.partner_code?.startsWith(x)) && status === 'success'
        }
        response.json(baseResponse);
        if (asynchronous)
            await contractProcessingRepo.updateProcessing(contractNumber, status, msg, responseUpload);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Lỗi máy chủ',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
        if (request.body?.contractNumber) {
            await contractProcessingRepo.updateProcessing(request.body.contractNumber, baseResponse.status, baseResponse.msg);
        }
    }
});

router.post('/customer-sign-only', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let responseUpload: ResponseUploadData = {};
        // let data = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber;
        let authorizeCode = request.body.authorizeCode;
        let identityCard = request.body.identityCard;
        let asynchronous = !!request.body.asynchronous;
        let newFileHost = request.body.newFileHost as boolean;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'Token không hợp lệ',
                });
            }
        }
        if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
            common.log("Face image is not verified");
            let baseResponse = {
                status: 'face not passed',
                msg: 'face not passed',
            }
            return response.status(400).json(baseResponse);
        }
        if (asynchronous) {
            let contractProcessing = await contractProcessingRepo.getContractProcessing(contractNumber);
            if (contractProcessing) {
                if (contractProcessing.message) {
                    response.status(200).json({
                        status: contractProcessing.status,
                        msg: contractProcessing.message,
                        responseUpload: JSON.parse(contractProcessing.response_upload)
                    });
                    setTimeout(() => {
                        contractProcessingRepo.deleteProcessing(contractProcessing.id);
                    }, 10000);
                    return;
                } else {
                    return response.status(200).json({
                        status: 'signing',
                        msg: 'Hợp đồng đang được ký'
                    });
                }
            }
        }
        let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
        if (resultContractNumberSigned) {
            let baseResponse = {
                status: 'fail',
                msg: 'Hợp đồng đã được ký',
            }
            return response.status(400).json(baseResponse);
        }
        if (asynchronous)
            await contractProcessingRepo.insertProcessing(contractNumber);
        let checkResult = await getContractInfo(contractNumber);
        if (checkResult.code === 'SUCCESS') {
            let checkContractRegisterResult = await esigningService.isContractRegistered(contractNumber);
            common.log(checkContractRegisterResult);
            if (checkContractRegisterResult.status === 'success') {
                let signResult = await esigningService.customerSign(contractNumber, checkContractRegisterResult.data.agreementUUID, authorizeCode, checkContractRegisterResult.data.billCode, { signEC: false, newFileHost });
                status = signResult.status;
                msg = signResult.msg;
                responseUpload = signResult.responseUpload?.data;
            } else {
                status = checkContractRegisterResult.status;
                msg = checkContractRegisterResult.msg;
            }
        } else {
            status = 'fail';
            msg = 'Không tìm thấy hợp đồng';
        }
        if (responseUpload?.bundleList?.length) {
            let bundleList = responseUpload.bundleList;
            let stringFile = JSON.stringify(bundleList)
            await contractService.saveListDocs(contractNumber, stringFile, 1);
            responseUpload.bundleList = [];
        }
        let baseResponse = {
            status: status,
            msg: msg,
            responseUpload: responseUpload,
            data: {
                contractNumber: contractNumber
            }
        }
        response.json(baseResponse);
        if (asynchronous)
            await contractProcessingRepo.updateProcessing(contractNumber, status, msg, responseUpload);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Lỗi máy chủ',
        }
        console.error('check contract error: ', error);
        response.status(error.status || 500).json(baseResponse);
        if (request.body?.contractNumber) {
            await contractProcessingRepo.updateProcessing(request.body.contractNumber, baseResponse.status, baseResponse.msg);
        }
    }
});

router.post('/signing-status', async (request, response) => {
    let token = request.headers?.token as string;
    let contractNumber = request.body.contractNumber as string;
    let identityCard = request.body.identityCard as string;

    let validateToken = await aaaApi.validateToken(token, contractNumber);
    if (!validateToken) {
        if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
            return response.status(400).json({
                status: 'invalid token',
                msg: 'Token không hợp lệ',
            });
        }
    }
    if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
        common.log("Face image is not verified");
        let baseResponse = {
            status: 'face not passed',
            msg: 'face not passed',
        }
        return response.status(400).json(baseResponse);
    }

    const contractProcessing = await contractProcessingRepo.getContractProcessing(contractNumber);
    if (contractProcessing) {
        if (contractProcessing.message) {
            let contractInfo = await contractService.findContractInfoByContractNumber(contractNumber);
            common.log(`${contractNumber}: ${contractProcessing.message}`);
            response.status(200).json({
                status: contractProcessing.status,
                msg: contractProcessing.message,
                responseUpload: JSON.parse(contractProcessing.response_upload),
                skipEmail: skipEmailPartners.find(x => contractInfo?.partner_code?.startsWith(x)) && contractProcessing.status === 'success'
            });
            await contractProcessingRepo.deleteProcessing(contractProcessing.id);
            return;
        } else {
            common.log(`${contractNumber}: Hợp đồng đang được ký`);
            return response.status(200).json({
                status: 'signing',
                msg: 'Hợp đồng đang được ký'
            });
        }
    }
    let resultContractNumberSigned = await esigningService.contractNumberSigned(contractNumber);
    if (resultContractNumberSigned) {
        let contractInfo = await contractService.findContractInfoByContractNumber(contractNumber);
        common.log(`${contractNumber}: Hợp đồng đã ký`);
        let baseResponse = {
            status: 'success',
            msg: 'Hợp đồng đã ký thành công',
            skipEmail: skipEmailPartners.find(x => contractInfo?.partner_code?.startsWith(x))
        }
        return response.status(200).json(baseResponse);
    }
    common.log(`${contractNumber}: Hợp đồng chưa được ký`);
    return response.status(200).json({
        status: 'fail',
        msg: 'Hợp đồng chưa được ký',
    });
});

router.post('/resend-otp', async function (request, response) {
    try {
        let status = null;
        let msg = null;
        let token = request.headers?.token as string;
        let contractNumber = request.body.contractNumber as string;
        let identityCard = request.body.identityCard as string;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            if (!(await esigningService.validateContractByIdCard(contractNumber, identityCard))) {
                return response.status(400).json({
                    status: 'invalid token',
                    msg: 'Token không hợp lệ',
                });
            }
        }
        if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
            common.log("Face image is not verified");
            let baseResponse = {
                status: 'face not passed',
                msg: 'face not passed',
            }
            return response.status(400).json(baseResponse);
        }
        let checkContractRegisterResult = await esigningService.isContractRegistered(contractNumber);
        if (checkContractRegisterResult.status === 'success') {
            // check otp resend > 5
            let cloudConfig = configService.getConfig();
            let timeLock = cloudConfig.data.fptEsignApi.timeLock;
            let baseResponseOTP = {
                status: 'FAILED_REQUEST',
                msg: 'Quá số lần gửi OTP',
                data: {
                    timeLock: timeLock
                }
            };
            let checkLockContract = await esigningService.getLockContract(contractNumber);
            if (checkLockContract) {
                return response.status(400).json(baseResponseOTP);
            } else {
                let exceededMaximum = await esigningService.findOptSend(checkContractRegisterResult.data.agreementUUID, contractNumber);
                if (exceededMaximum) return response.status(400).json(baseResponseOTP);
            }
            // check Old OTP is still valid
            let checkOtpDue = await esigningService.findOtpDue(contractNumber);
            if (checkOtpDue) {
                let baseResponse = {
                    status: 'FAILED_REQUEST',
                    msg: 'OTP cũ vẫn còn hiệu lực',
                }
                return response.status(400).json(baseResponse);
            }

            let resultRegenerate = await esigningService.regenerateAuthorizationCode(checkContractRegisterResult.data.agreementUUID, contractNumber);
            if (resultRegenerate.status === 'success') {
                status = 'success';
                msg = 'Gửi OTP thành công';

                smsService.sendSmsOtpToSign(checkContractRegisterResult.data.phoneNumber, resultRegenerate.authorizeCode, contractNumber, "contract");
            } else {
                status = resultRegenerate.status;
                msg = resultRegenerate.msg;
            }
        } else {
            status = checkContractRegisterResult.status;
            msg = checkContractRegisterResult.msg;
        }
        let baseResponse = {
            status: status,
            msg: msg,
            data: {
                contractNumber: request.body.contractNumber,
                authorizeCodeExpireTime: 300
            }
        }
        response.json(baseResponse);
    } catch (error) {
        let baseResponse = {
            status: 'server_error',
            msg: 'Lỗi máy chủ',
        }
        console.error('check contract error: ', error.message);
        response.status(error.status || 500).json(baseResponse);
    }
});

router.post("/web-log", (request: Request, response: Response) => {
    let message = request.body.message as string;
    let contractNumber = request.body.contractNumber as string;
    if (message) {
        common.log(message, "web-client" + (contractNumber ? "-" + contractNumber : ""));
    }
    response.send({ message: "OK" });
});

router.post("/face-pass", async (req: Request, res: Response) => {
    let { contractNumber } = req.body;
    let token = req.headers?.token as string;

    let validateToken = await aaaApi.validateToken(token, contractNumber);
    if (!validateToken) {
        let baseResponse = {
            status: 'invalid token',
            msg: 'Token không hợp lệ',
        }
        return res.status(400).json(baseResponse);
    }

    if (!(await faceService.validateTokenFaceCheck(contractNumber, token))) {
        common.log("Face image is not verified");
        let baseResponse = {
            status: 'face not passed',
            msg: 'face not passed',
        }
        return res.json(baseResponse);
    }
    return res.json({
        status: 'success',
        msg: 'face is verified'
    });
});

router.post("/selfie-result", async (req: Request, res: Response) => {
    let { contractNumber, id } = req.body;
    let token = req.headers?.token as string;

    let validateToken = await aaaApi.validateToken(token, contractNumber);
    if (!validateToken) {
        let baseResponse = {
            status: 'invalid token',
            msg: 'Token không hợp lệ',
        }
        return res.status(400).json(baseResponse);
    }

    let selfie = await selfieImageRepo.getSelfieById(id);
    if (selfie?.status) {
        let baseResponse = {
            status: 'success',
            msg: 'Đã có kết quả',
            data: {
                status: selfie.status,
                message: selfie.response_message
            }
        }
        return res.json(baseResponse);
    }
    return res.json({
        status: 'success',
        msg: 'Đang xử lý',
    });
});

router.post("/face-verify", async (req: Request, res: Response) => {
    let responseObj = {
        code: 0,
        msg: "",
        blocked: false
    };
    let responsed = false;
    let item: Partial<SelfieImageEntity>;
    try {
        let { contractNumber, locationResponse, ipAddress } = req.body;
        let token = req.headers?.token as string;
        let selfieImg = Array.isArray(req.files.image) ? req.files.image[0] : req.files.image;
        let idCardImg = Array.isArray(req.files.idImage) ? req.files.idImage[0] : req.files.idImage;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return res.status(400).json(baseResponse);
        }
        let selfieBlocked = await faceService.checkBlockSelfie(contractNumber);
        if (selfieBlocked) {
            common.log("Contract blocked");
            responseObj.code = 1;
            responseObj.msg = 'Hợp đồng bị khóa kí trong 24h';
            responseObj.blocked = true;
            return res.json(responseObj);
        }

        let contract = await contractService.findContractInfoByContractNumber(contractNumber);
        if (!contract) {
            return res.json({ code: 1, msg: 'Hợp đồng không tồn tại' });
        }
        let { faceCheck, idCardCheck, locationCheck } = contract.flags;

        if (faceCheck && !selfieImg) {
            return res.json({ code: 1, msg: 'Thiếu ảnh chân dung' });
        }
        if (idCardCheck && !idCardImg) {
            return res.json({ code: 1, msg: 'Thiếu ảnh CMND/CCCD' });
        }
        if (locationCheck && !locationResponse) {
            return res.json({ code: 1, msg: 'Thiếu thông tin GPS' });
        }

        let location = JSON.parse(locationResponse || "{}");
        item = {
            contract_number: contractNumber,
            token,
            location_response: JSON.stringify(location),
            latitude: location.latitude,
            longitude: location.longitude,
            location_name: location.localityInfo?.administrative?.map(x => x.name)?.reverse()?.join(", "),
            ip_address: ipAddress ?? requestIp.getClientIp(req),
        };
        let result = await selfieImageRepo.insertLog(item);
        item.id = result.id;
        responsed = true;
        res.json({ code: 0, msg: 'Upload success', data: result.id })
        const randomText = crypto.randomInt(1000000000).toString();
        let file1Uploaded: ManagedUpload.SendData, file2Uploaded: ManagedUpload.SendData;
        await Promise.all([
            (async () => {
                if (location.latitude && location.longitude) {
                    let mapImageId = await googleApi.getMapImage(location.latitude, location.longitude, 17);
                    if (mapImageId) {
                        item.map_image_id = mapImageId;
                    }
                }
            })(),
            (async () => {
                if (faceCheck) {
                    let selfieFile = await documentService.convertImageToPdf(selfieImg);
                    let selfieFileName = 'esigning/selfie/' + contractNumber + "_" + randomText + ".pdf";
                    file1Uploaded = await fileService.uploadFile(selfieFileName, selfieFile);
                }
            })(),
            (async () => {
                if (idCardCheck) {
                    let idCardFile = await documentService.convertImageToPdf(idCardImg);
                    let idCardFileName = 'esigning/idCard/' + contractNumber + "_" + randomText + ".pdf";
                    file2Uploaded = await fileService.uploadFile(idCardFileName, idCardFile);
                }
            })()
        ]);

        item.image_id = file1Uploaded?.Key;
        item.id_card_image_id = file2Uploaded?.Key;
        let deResult = await deApi.faceVerify(file1Uploaded?.Key, file2Uploaded?.Key, contractNumber, file1Uploaded?.Bucket, contract);
        if (deResult) {
            item.response_code = deResult.code;
            item.response_message = deResult.msg;
            responseObj.code = deResult.code;
            responseObj.msg = deResult.msg;
        } else {
            responseObj.code = -1;
            responseObj.msg = "Check ekyc failed";
        }
    } catch (error) {
        common.error(error);
        responseObj.code = -1;
        responseObj.msg = "Internal server error";
    } finally {
        if (item && item.image_id) {
            if (item.response_code === 0)
                item.status = "SUCCESS";
            else item.status = "FAILED";
            await selfieImageRepo.updateLog(item);
            if (item.status === "FAILED") {
                let blocked = await faceService.detectBlockSelfie(item.contract_number);
                if (blocked) {
                    responseObj.blocked = true;
                }
            }
        }
    }
    if (!responsed) {
        res.json(responseObj);
    }
});

router.post("/fiz/face-verify", async (req: Request, res: Response) => {
    let responseObj = {
        code: 0,
        msg: "",
        blocked: false
    };
    let responsed = false;
    let item: Partial<SelfieImageEntity>;
    try {
        let { contractNumber, locationResponse, ipAddress } = req.body;
        let token = req.headers?.token as string;
        let selfieImg = Array.isArray(req.files.image) ? req.files.image[0] : req.files.image;
        let idCardImg = Array.isArray(req.files.idImage) ? req.files.idImage[0] : req.files.idImage;

        let validateToken = await aaaApi.validateToken(token, contractNumber);
        if (!validateToken) {
            let baseResponse = {
                status: 'invalid token',
                msg: 'invalid token',
            }
            return res.status(400).json(baseResponse);
        }
        let selfieBlocked = await faceService.checkBlockSelfie(contractNumber);
        if (selfieBlocked) {
            common.log("Contract blocked");
            responseObj.code = 1;
            responseObj.msg = 'Hợp đồng bị khóa kí trong 24h';
            responseObj.blocked = true;
            return res.json(responseObj);
        }

        let contract = await contractService.findContractInfoByContractNumber(contractNumber);
        if (!contract) {
            return res.json({ code: 1, msg: 'Hợp đồng không tồn tại' });
        }
        let { faceCheck, idCardCheck, locationCheck } = contract.flags;

        if (faceCheck && !selfieImg) {
            return res.json({ code: 1, msg: 'Thiếu ảnh chân dung' });
        }
        if (idCardCheck && !idCardImg) {
            return res.json({ code: 1, msg: 'Thiếu ảnh CMND/CCCD' });
        }
        if (locationCheck && !locationResponse) {
            return res.json({ code: 1, msg: 'Thiếu thông tin GPS' });
        }

        let location = JSON.parse(locationResponse || "{}");
        item = {
            contract_number: contractNumber,
            token,
            location_response: JSON.stringify(location),
            latitude: location.latitude,
            longitude: location.longitude,
            location_name: location.localityInfo?.administrative?.map(x => x.name)?.reverse()?.join(", "),
            ip_address: ipAddress ?? requestIp.getClientIp(req),
        };
        let result = await selfieImageRepo.insertLog(item);
        item.id = result.id;
        responsed = true;
        res.json({ code: 0, msg: 'Upload success', data: result.id })
        const randomText = crypto.randomInt(1000000000).toString();
        let file1Uploaded: ManagedUpload.SendData, file2Uploaded: ManagedUpload.SendData;
        await Promise.all([
            (async () => {
                if (location.latitude && location.longitude) {
                    let mapImageId = await googleApi.getMapImage(location.latitude, location.longitude, 17);
                    if (mapImageId) {
                        item.map_image_id = mapImageId;
                    }
                }
            })(),
            (async () => {
                if (faceCheck) {
                    let selfieFile = await documentService.convertImageToPdf(selfieImg);
                    let selfieFileName = 'esigning/selfie/' + contractNumber + "_" + randomText + ".pdf";
                    file1Uploaded = await fileService.uploadFile(selfieFileName, selfieFile);
                }
            })(),
            (async () => {
                if (idCardCheck) {
                    let idCardFile = await documentService.convertImageToPdf(idCardImg);
                    let idCardFileName = 'esigning/idCard/' + contractNumber + "_" + randomText + ".pdf";
                    file2Uploaded = await fileService.uploadFile(idCardFileName, idCardFile);
                }
            })()
        ]);

        item.image_id = file1Uploaded?.Key;
        item.id_card_image_id = file2Uploaded?.Key;
        let deResult = await deApi.faceVerifyV2(file1Uploaded?.Key, contractNumber, contract);
        if (deResult && deResult.is_valid == 1) {
            item.response_code = deResult.code;
            item.response_message = deResult.message;
            responseObj.code = deResult.code;
            responseObj.msg = deResult.message;
        } else {
            item.response_code = -1;
            item.response_message = "Ảnh chụp không khớp! Vui lòng chụp lại ảnh!";
            responseObj.code = -1;
            responseObj.msg = "Face verify failed";
        }
    } catch (error) {
        item.response_code = -1;
        item.response_message = "Hệ thống tạm thời gián đoạn! Vui lòng thử lại sau 1 đến 2 giờ";

        common.error(error);
        responseObj.code = -1;
        responseObj.msg = "Internal server error";
    } finally {
        if (item && item.image_id) {
            if (item.response_code === 0)
                item.status = "SUCCESS";
            else item.status = "FAILED";
            await selfieImageRepo.updateLog(item);
            if (item.status === "FAILED") {
                let blocked = await faceService.detectBlockSelfie(item.contract_number);
                if (blocked) {
                    responseObj.blocked = true;
                }
            }
        }
    }
    if (!responsed) {
        res.json(responseObj);
    }
});

export default router;