/**
 * Main application
 */
import express, { NextFunction, Request, Response } from 'express';
import dotenv from 'dotenv';
import cors from "cors";
import http from "http";
import expressFileupload from 'express-fileupload';
import * as configService from './services/config-service';
import * as common from './utils/common';
import contractRouter from './router/contract-router';
import esigningRouter from './router/esigning-router';
import esigningDrRouter from './router/esigning-dr-router';
import misaRouter from './router/partners/misa-router';
import dnseRouter from './router/partners/dnse-router';
import gimoRouter from './router/partners/gimo-router';
import fundRouter from './router/partners/fund-router';
import manfRouter from './router/partners/manf-router';
import bizzRouter from './router/partners/bizz-router';
import vuipRouter from './router/partners/vuip-router';
import internalRouter from './router/internal-router';
import easyUiRouter from './router/easy-ui/easy-ui-router';
import httpContext from 'express-http-context';
import { nanoid } from 'nanoid';
import { oauthApi } from './api';
import { initPool } from './datapool/pools';
import { abTestingService, faceService } from './services';

const app = express();

const httpServer = new http.Server(app);
app.use(cors())
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(expressFileupload({
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
}));
app.use(httpContext.middleware);

common.setServiceName('esigning');
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = "0";//by pass ssl
// fallback
if (!process.env.HOST_ENV) {
  const path = '.env.' + process.env.NODE_ENV;
  dotenv.config({ path: path });
}

app.use((request, response, next) => {
  //cors()
  if (request.originalUrl.startsWith("/esigning/v1/healthCheck")) {
    next();
    return;
  }
  httpContext.set("requestId", nanoid());
  if (request.method === "POST") {
    common.log('request: ' + request.originalUrl + " body: " + JSON.stringify(request.body));
    if (request.files) {
      for (const key in (request.files ?? [])) {
        const fileItem = request.files[key];
        const arr = Array.isArray(fileItem) ? fileItem : [fileItem];
        for (const element of arr) {
          common.log(`file uploaded: ${JSON.stringify({
            name: element.name,
            size: element.size,
            truncated: element.truncated,
            mimetype: element.mimetype,
          })}`);
          if (element.truncated) {
            common.log("Error: file size too large")
            response.status(400).json({
              status: 'fail',
              msg: 'File quá lớn',
            });
            return;
          }
        }
      }
    }
  } else {
    common.log("request: " + request.originalUrl);
  }
  next();
})
app.use((req: Request & { disableLog: boolean }, res: Response, next: NextFunction) => {
  try {
    let oldEnd = res.end;
    let chunks: any[] = [];
    res.end = function (chunk) {
      let method = req.method;
      try {
        if (method !== "GET" && !req.disableLog) {
          if (chunk) chunks.push(chunk);
          let body = chunk;
          if (Buffer.isBuffer(body)) body = Buffer.concat(chunks).toString("utf8");
          common.log('response: ' + body);
        }

        oldEnd.apply(res, arguments);
      } catch (error) {
        common.log(`response log error ` + JSON.stringify(error, Object.getOwnPropertyNames(error)));
        oldEnd.apply(res, arguments);
        next();
      }
    };
  } catch (error) {
    common.log(`Error set response lv1 ${req.headers?.requestId} error ${error}`);
  }
  next();
});

app.use((err: any, request: Request, response: Response, next: NextFunction) => {
  common.error(err)
  let baseResponse = {
    status: 'server_error',
    msg: 'Lỗi hệ thống, vui lòng thử lại sau',
  }
  response.status(err.status || 500).json(baseResponse);
})

//health check
app.get('/esigning/v1/healthCheck', function (request, response) {
  response.json({ message: 'Environment: ' + (process.env.NODE_ENV || 'dev') + '. Esigning-Service is alive. 23:25 27/9' });
})

app.get('/esigning/v1/reload-config', async (request, response) => {
  try {
    await faceService.reloadConfig();
    await abTestingService.reloadConfig();
    let result = await configService.reloadConfig();
    if (result) {
      response.send({ code: 0, message: "config reloaded" })
    } else {
      response.send({ code: 1, message: "reload config failed" })
    }
  } catch (error) {
    response.send({ code: 1, message: "reload config failed" })
  }
});

app.use('/esigning/v1/contract', contractRouter);
app.use('/esigning/v1', esigningRouter);
app.use('/esigning-dr/v1', esigningDrRouter);
app.use('/esigning/internal/misa', misaRouter);
app.use('/esigning/internal/dnse', dnseRouter);
app.use('/esigning/internal/gimo', gimoRouter);
app.use('/esigning/internal/fund', fundRouter);
app.use('/esigning/internal/manf', manfRouter);
app.use('/esigning/internal/bizz', bizzRouter);
app.use('/esigning/internal/vuip', vuipRouter);
app.use('/esigning/internal', internalRouter);
app.use('/esigning/internal/easy-ui', easyUiRouter);

(async () => {
  let cloudConfig = await common.getAPI<configService.CloudConfig>(
    process.env.HOST_CONFIGURATION + "/services/?service=esigning",
    { 'Content-type': 'application/json' }
  );
  common.log('[Start] loading config');
  if (!cloudConfig || cloudConfig.status != 1) {
    common.log('Cannot load configuration from config server');
  } else {
    configService.loadConfig(cloudConfig);
    initPool();
    await faceService.reloadConfig();
    await abTestingService.reloadConfig();
    common.log('[End] loading config');

    httpServer.listen(cloudConfig.data.http.port);
    common.log("Service started on port: 5656");
    oauthApi.loadOauthInfo();
  }
})();