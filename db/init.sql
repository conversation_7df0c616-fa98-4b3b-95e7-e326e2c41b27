-- Install uuid extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"

-- Create TABLE ON POSGRESQL.
CREATE TABLE esigning_contract_rule_info (
    rule_id uuid DEFAULT uuid_generate_v4(),
    rule_name character varying(255),
    rule_content character varying(500),
    default_agree boolean,
    allowed_edit boolean,
    created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_date timestamp with time ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_deleted boolean DEFAULT false,
    PRIMARY KEY (rule_id)
);

CREATE TABLE esigning_terms_conditions_agree_info (
    term_id uuid DEFAULT uuid_generate_v4(),
    agree_content character varying(500),
    default_agree boolean,
    allowed_edit boolean,
    created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_date timestamp with time ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_deleted boolean DEFAULT false,
    PRIMARY KEY (term_id)
);

CREATE TABLE esigning_contract_rule_chosen_list (
    id uuid DEFAULT uuid_generate_v4(),
    rule_id character varying(255),
    contract_number character varying(255),
    agreed boolean,
    created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE esigning_terms_conditions_agreed_list (
    id uuid DEFAULT uuid_generate_v4(),
    term_id character varying(255),
    contract_number character varying(255),
    agreed boolean,
    created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE esigning_prepare_certificate_info (
	contract_number varchar(255) NOT NULL,
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	country varchar(255) NULL,
	current_agreement_uuid varchar(255) NULL,
    agreement_uuid_created_date timestamp NULL,
	location varchar(255) NULL,
	mobile_no varchar(255) NULL,
	partner_code varchar(255) NULL,
	personal_id varchar(255) NULL,
	personal_name varchar(255) NULL,
	response_code int4 NULL,
	response_message varchar(255) NULL,
	state_province varchar(255) NULL,
	PRIMARY KEY (contract_number)
);

CREATE TABLE esigning_agreement_uuid_created_history (
	id uuid DEFAULT uuid_generate_v4(),
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	agreement_uuid varchar(255) NULL,
	response_code int4 NULL,
	response_message varchar(255) NULL,
	contract_number varchar(255) NULL,
	PRIMARY KEY (id)
);

CREATE TABLE esigning_prepare_file_info (
	id uuid DEFAULT uuid_generate_v4(),
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	agreement_uuid varchar(255) NULL,
	authorize_code varchar(255) NULL,
	bill_code varchar(255) NULL,
	contract_number varchar(255) NULL,
	response_code int4 NULL,
	response_message varchar(255) NULL,
	PRIMARY KEY (id)
);

CREATE TABLE esigning_authorize_counter_signing (
	id uuid DEFAULT uuid_generate_v4(),
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	agreement_uuid varchar(255) NULL,
	authorize_code varchar(255) NULL,
	bill_code varchar(255) NULL,
	contract_number varchar(255) NULL,
	ec_signed_response_code int4 NULL,
	ec_signed_response_message varchar(255) NULL,
	response_code int4 NULL,
	response_message varchar(255) NULL,
	PRIMARY KEY (id)
);

CREATE TABLE esigning_file_data_signed (
	id uuid DEFAULT uuid_generate_v4(),
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	agreement_uuid varchar(255) NULL,
	contract_number varchar(255) NULL,
	file_name varchar(255) NULL,
	signed_file_data bytea NULL,
	PRIMARY KEY (id)
);

CREATE TABLE esigning_regenerate_authorization_code (
	id uuid DEFAULT uuid_generate_v4(),
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	agreement_uuid varchar(255) NULL,
	authorize_code varchar(255) NULL,
	contract_number varchar(255) NULL,
    expire_date timestamp NULL,
	response_code int4 NULL,
	response_message varchar(255) NULL,
	PRIMARY KEY (id)
);


CREATE TABLE esigning_contract_info (
	contract_number varchar(255) NOT NULL,
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	customer_name varchar(255) NULL,
	identity_card_id varchar(255) NULL,
	phone_number varchar(255) NULL,
	customer_address varchar(255) NULL,
	state_province varchar(255) NULL,
	country varchar(255) NULL,
	partner_code varchar(255) NULL,
	status_code varchar(255) NULL,
	PRIMARY KEY (contract_number)
);

CREATE TABLE public.esigning_contract_annex_info (
	annex_number varchar(255) NOT NULL,
	created_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	product_name varchar(255) NULL,
    product_code varchar(255) NULL,
    contract_number varchar(255) NULL,
    contract_date timestamp NULL,
    customer_name varchar(255) NULL,
    birthday timestamp NULL,
    id_number varchar(255) NULL,
    issue_date varchar(255) NULL,
    issue_place varchar(255) NULL,
    current_address varchar(255) NULL,
    current_ward varchar(255) NULL,
    current_district varchar(255) NULL,
    current_province varchar(255) NULL,
    permanent_address varchar(255) NULL,
    permanent_ward varchar(255) NULL,
    permanent_district varchar(255) NULL,
    permanent_province varchar(255) NULL,
    phone_number varchar(255) NULL,
    email varchar(255) NULL,
    request_name varchar(255) NULL,
    request_date timestamp NULL,
    restructuring_time int4 NULL,
    overdue_interest decimal NULL,
    ri_rate_month decimal NULL,
    ri_rate_year decimal NULL,
    gpp bool NULL DEFAULT false,
    gpp_month int4 NULL,
    gpp_rate_month decimal NULL,
    gpp_rate_year decimal NULL,
    gpi bool NULL DEFAULT false,
    gpi_month int4 NULL,
    gpi_rate_month decimal NULL,
    gpi_rate_year decimal NULL,
    status_code varchar(255) NULL,
	CONSTRAINT esigning_contract_annex_info_pkey PRIMARY KEY (annex_number)
);

--insert master data
insert into esigning_terms_conditions_agree_info(agree_content, default_agree, allowed_edit) values ('Đã đọc, hiểu rõ và đồng ý với các nội dung tại Hợp đồng cũng như bản Điểu khoản và Điều kiện giao dịch chung của Easy Credit', true, true);
insert into esigning_terms_conditions_agree_info(agree_content, default_agree, allowed_edit) values ('Đồng ý đăng ký và sử dụng chữ ký số của Đơn vị cung cấp dịch vụ chữ ký số FPT CA để ký điện tử văn bản, hợp đồng điện tử với Easy Credit', true, true);
insert into esigning_terms_conditions_agree_info(agree_content, default_agree, allowed_edit) values ('Đã đọc, hiểu rõ, đồng ý và cam kết tuân thủ các điều kiện và điều khoản sử dụng dịch vụ chứng thực', true, true);

INSERT INTO esigning_contract_rule_info (rule_id,rule_name,rule_content,default_agree,allowed_edit,created_date,updated_date,is_deleted) VALUES 
('73280de8-da15-4d01-ab81-23a7c16f7ee4','Điều 2.4','Bên vay đồng ý cho EASY CREDIT giới thiệu cho bên vay các sản phẩm và dịch vụ của EASY CREDIT',true,true,'2021-04-03 12:45:53.005','2021-04-03 12:45:53.005',false)
,('93fc2e80-1922-49e9-94cd-60066eff0611','Điều 3','Bên vay xác nhận rằng mình không có người có liên quan có khoản vay tại EASY CREDIT',true,true,'2021-04-03 12:45:53.019','2021-04-03 12:45:53.019',false)
;

INSERT INTO esigning_terms_conditions_agree_info (term_id,agree_content,default_agree,allowed_edit,created_date,updated_date,is_deleted) VALUES 
('7768fd51-34fd-4953-89cb-7b5637a39a46','Đã đọc, hiểu rõ và đồng ý với các nội dung tại Hợp đồng cũng như bản Điểu khoản và Điều kiện giao dịch chung của Easy Credit',true,true,'2021-04-03 13:37:58.609','2021-04-03 13:37:58.609',false)
,('69577e1e-ec0c-441e-845b-f838a8d17c29','Đồng ý đăng ký và sử dụng chữ ký số của Đơn vị cung cấp dịch vụ chữ ký số FPT CA để ký điện tử văn bản, hợp đồng điện tử với Easy Credit',true,true,'2021-04-03 13:37:58.609','2021-04-03 13:37:58.609',false)
,('2ac64177-d6db-4974-b90d-1678cbdb8108','Đã đọc, hiểu rõ, đồng ý và cam kết tuân thủ các điều kiện và điều khoản sử dụng dịch vụ chứng thực',true,true,'2021-04-03 13:37:58.609','2021-04-03 13:37:58.609',false)
;