export type ContractInfoStatus = "SIGNING_IN_PROGRESS" | "SINGING_IN_PROGRESS" | "SIGING_IN_PROGRESS" | "DUPLICATED" | "APPROVED";
export type ContractAnnexStatus = "SIGNING" | "SIGNED" | "FULL_SIGNED";
export type ContractSystemType = "credit" | "merchant" | "vinfast" | "cashloan" | "united";
export type ResponseStatus = "success" | "fail" | "server_error";
export type SmsType = "contract" | "dbr_contract" | "dbr_schedule";
export type ContractInfoFlags = {
    faceCheck?: boolean;
    idCardCheck?: boolean;
    locationCheck?: boolean;
};

/** Thông tin hợp đồng */
export interface ContractInfoEntity {
    /** Mã hợp đồng */
    contract_number: string,
    /** Tên khách hàng */
    customer_name: string,
    /** Số CMND/CCCD */
    identity_card_id: string,
    /** <PERSON><PERSON> điện thoại */
    phone_number: string,
    /** Đ<PERSON>a chỉ */
    customer_address: string,
    country?: string,
    state_province: string,
    partner_code: string,
    /** <PERSON><PERSON><PERSON> trị: SIGNING_IN_PROGRESS, SINGING_IN_PROGRESS, SIGING_IN_PROGRESS, DUPLICATED, APPROVED */
    status_code: ContractInfoStatus,
    flags: ContractInfoFlags,
    spic_path: string;
    request_id: string;

    pid_path?: string;
    pid1_path?: string;
}
export interface UploadDocsEntity {
    contract_number: string,
    response: string,
    status: number,
}
export interface AuthorizeCounterSigningEntity {
    agreement_uuid: string,
    authorize_code: string,
    bill_code: string,
    contract_number: string,
    ec_signed_response_code: number,
    ec_signed_response_message: string,
    response_code: number,
    response_message: string,
}
export interface ContractRuleInfoEntity {
    rule_id: string,
    rule_name: string,
    rule_content: string,
    default_agree: boolean,
    allowed_edit: boolean,
    service: string,
    key_selection: string,
}
export interface ContractRuleItem {
    ruleId: string,
    ruleName: string,
    ruleContent: string,
    defaultAgree: boolean,
    keySelection: string,
    allowedEdit: boolean,
}
export interface ContractRuleChosenListEntity {
    rule_id: string,
    contract_number: string,
    agreed: boolean,
}
export interface ContractRuleChosenItemRequest {
    ruleId: string,
    agreed: boolean,
}
export interface FileDataSignedEntity {
    file_name: string,
    signed_file_data: any,
    agreement_uuid: string,
    contract_number: string,
    s3_key?: string,
}
export interface FptResponse {
    responseCode: number,
    responseMessage: string,
    authorizeCredential: string,
    billCode: string,
}
export interface PrepareCertificateInfoEntity {
    response_code: number,
    agreement_uuid_created_date: Date,
    current_agreement_uuid: string,
    mobile_no: string,
}
export interface PrepareFileInfoEntity {
    agreement_uuid: string,
    bill_code: string,
}
export interface RegenerateAuthorizationCodeEntity {
    agreement_uuid: string,
    authorize_code: string,
    contract_number: string,
    response_code: number,
    response_message: string,
    expire_date?: Date,
}
export interface TermsConditionsAgreeInfoEntity {
    term_id: string,
    agree_content: string,
    default_agree: boolean,
    allowed_edit: boolean,
}
export interface TermsConditionsAgreedListEntity {
    term_id: string,
    contract_number: string,
    agreed: boolean,
}
export interface TermsAndConditionsDTO {
    termId: string,
    termContent: string,
    defaultAgree: boolean,
    allowedEdit: boolean,
}
export type ContractInfoLosResponse = {
    code: number | string,
    msg: string,
    data?: {
        id: string
        af1RequestId: any
        af2RequestId: string
        channel: string
        partnerCode: string
        contractNumber: string
        lmsContract: any
        dsaAgentCode: string
        productId: string
        productType: any
        productCode: string
        obserCode: any
        custId: string
        customerName: string
        gender: string
        dateOfBirth: string
        identityCard: string
        issueDate: string
        issuePlace: string
        phoneNumber: string
        email: any
        disbursementMethod: string
        accountNumber: any
        bankCode: any
        bankName: any
        branchCode: any
        branchName: any
        beneficiaryName: any
        temCountry: any
        temProvince: string
        temProvinceCode: string
        temDistrict: string
        temDistrictCode: string
        temWard: string
        temWardCode: string
        temDetailAddress: string
        permanentCountry: any
        permanentProvince: string
        permanentProvinceCode: string
        permanentDistrict: string
        permanentDistrictCode: string
        permanentWard: string
        permanentWardCode: string
        permanentDetailAddress: string
        mailingAddress: string
        nationality: any
        reference1: string
        reference2: string
        relativeReferenceName1: string
        relativeReferenceName2: string
        relativeReferencePhone1: string
        relativeReferencePhone2: string
        otherContact: string
        contactDetail: any
        occupation: any
        monthlyIncome: number
        otherIncome: number
        monthlyExpenses: number
        companyName: string
        companyAddress: string
        companyCountry: any
        companyProvince: string
        companyDistrict: string
        companyWard: string
        companyPhoneNumber: string
        contractFrom: number
        contractTo: number
        jobType: string
        jobTitle: string
        employmentType: string
        employmentContractType: any
        marriedStatus: string
        houseType: string
        otherHouseType: any
        numOfDependents: number
        yearsOfStay: any
        incomeProof: any
        salaryFrequency: string
        salaryMethod: string
        salaryPaymentDate: string
        taxId: any
        loanPurpose: string
        loanAmount: number
        loanTenor: number
        partnerScore: string
        creditScore: any
        fraudScore: any
        scoreMatching: any
        typeMatching: any
        ocrCompared: any
        tsaScore: any
        fizoEkyc: string
        fizoComment: string
        otpCode: any
        imageIdCard: any
        imageSelfie: any
        af1StatusCode: any
        af1Status: any
        af2StatusCode: number
        af2Status: string
        statusCode: number
        status: string
        rjSubcodeCode: any
        rjSubcode: any
        af1SaveDate: string
        af1CreatedOn: string
        af1UpdatedOn: any
        af2SaveDate: string
        af2CreatedOn: string
        af2UpdatedOn: string
        createdOn: string
        updatedOn: string
        lastUpdatedOn: any
        activeDate: any
        startDate: any
        endDate: any
        terminateDate: any
        cancelDate: any
        stage: any
        sysType: any
        contractType: number | string
        incomeProofFlag: any
        bankAccountOwner: any
        bankCity: any
        phoneNumber2: any
        phoneNumber3: any
        phoneVerifiedTickBox: any
        mHouseholdExpenses: any
        otherContactType: any
        emplIndustry: any
        emplCtrctDuration: any
        emplType: any
        education: any
        custType: any
        sameCities: any
        countryPer: any
        villageCur: any
        villagePer: any
        idType: any
        imxStatus: any
        requestIntRate: any
        requestInstalAmt: any
        isDeleted: number
        identityCardOther: string
        leadCode: any
        otherReference: any
        peCode: any
        scoring: any
        timesCheckSanity: any
        cancelBy: any
        contractNumberParent: any
        slAdvanceContractType: any
        username: any
        staffNumber: any
        referralCode: any
        requestIdRef: any
        insuranceType: any
        partnerCreatedDate: string
        partnerSendDate: string
        selfieDocSource: string
        idcardDocSource: string
        companyCode: any
        requestDisbMethod: string
        idQrCode: any
        isSkipEkyc: any
        idCardNumber: string
        pathSpic: string
        requestId: string
        pidDocs: Array<{
            id: string
            doc_type: string
            file_name: string
            file_path: string
            url: string
        }>
    }
}
export type VerifyLoanLosResponse = {
    code: number | string,
    message: string,
    data?: {
        isVerifyLoan?: boolean
        messageVerifyLoan?: string
        decision?: string
    }
}
export type CancelLoanContractLosResponse = {
    code: number;
    message: string;
};
export type RefuseLoanContractLosResponse = {
    code: number;
    message: string;
};
export type ContractDbrInfo = {
    identity_card: string;
    date_of_birth: Date;
    customer_name: string;
    entry_date: Date;
    tem_address: string;
    permanent_address: string;
    channel: string;
    partner_code: string;
    contract_number?: string;
    phone_number?: string;
    current_province?: string;
}
export type ContractProcessingEntity = {
    id: number;
    contract_number: string;
    status: string;
    message: string;
    response_upload: string | null | undefined;
}
export type ContractAnnexInfoEntity = {
    product_name: string,
    product_code: string,
    annex_number: string,
    contract_number: string,
    contract_date: Date,
    customer_name: string,
    birthday: Date,
    id_number: string,
    issue_date: string,
    issue_place: string,
    current_address: string,
    current_ward: string,
    current_district: string,
    current_province: string,
    permanent_address: string,
    permanent_ward: string,
    permanent_district: string,
    permanent_province: string,
    phone_number: string,
    email: string,
    request_name: string,
    request_date: Date,
    restructuring_time: number,
    overdue_interest: number,
    ri_rate_month?: number,
    ri_rate_year: number,
    gpp: boolean,
    gpp_month?: number,
    gpp_rate_month?: number,
    gpp_rate_year?: number,
    gpi: boolean,
    gpi_month?: number,
    gpi_rate_month?: number,
    gpi_rate_year?: number,
    status_code: ContractAnnexStatus,
    maturity_date?: Date,
    prin_debt?: number,
}
export type ResponseUpload = {
    data?: ResponseUploadData;
}
export type ResponseUploadData = {
    bundleList?: string | any[];
}
export type SelfieImageEntity = {
    id: number;
    contract_number: string;
    image_id: string;
    token: string;
    status: "SUCCESS" | "FAILED" | "BLOCK" | "MANUAL" | "MANUAL_PASS";
    response_code: number;
    response_message: string;
    ip_address: string;
    latitude: string;
    longitude: string;
    location_name: string;
    location_response: string;
    map_image_id: string;
    id_card_image_id: string;
    created_date: Date;
}
export type SelfieSettingEntity = {
    id: number;
    partner_code: string;
    enabled: number;
}
export type AbTestingConfig = {
    id: number;
    partner_code: string;
    data: string;
    start_time?: Date;
    end_time?: Date;
    is_delete?: boolean;

    branches: ContractInfoFlags[];
};

export interface IProcessEnv {
    DB_HOST_WRITE: string;
    DB_HOST_READ: string;
    DB_DATABASE: string;
    DB_PORT: string;
    DB_USER: string;
    DB_PASSWORD: string;
    HOST_CONFIGURATION: string;
    HOST_ENV: string;

    NODE_ENV: string;
}

/* eslint-disable */
declare global {
    namespace NodeJS {
        interface ProcessEnv extends IProcessEnv { }
    }
}
/* eslint-enable */