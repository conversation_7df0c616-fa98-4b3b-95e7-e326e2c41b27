import * as dataPools from '../datapool/pools';
import { FptResponse, PrepareFileInfoEntity } from '../types';
import * as common from "../utils/common";

export async function insertPrepareFile(agreementUUID: string, contractNumber: string, fptResponse: FptResponse) {
    try {
        let sqlInsert = 'insert into esigning_prepare_file_info(agreement_uuid, contract_number, authorize_code, bill_code, response_code, response_message) values ($1, $2, $3, $4, $5, $6)';
        let poolWrite = dataPools.getPoolWrite();
        await poolWrite.query(sqlInsert, [agreementUUID, contractNumber, fptResponse.authorizeCredential, fptResponse.billCode, fptResponse.responseCode, fptResponse.responseMessage]);
    } catch (error) {
        common.log('insertPrepareFile error: ');
        common.error(error);
    }
}

export async function findPrepareFileSuccessByAgreementUUID(agreementUUID: string) {
    let sqlSelect = 'select * from esigning_prepare_file_info where agreement_uuid = $1 and response_code = 1007';
    let poolWrite = dataPools.getPoolWrite();
    let resultData = await poolWrite.query<PrepareFileInfoEntity>(sqlSelect, [agreementUUID]);
    return resultData;
}
