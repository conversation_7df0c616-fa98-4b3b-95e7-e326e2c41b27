import * as dataPools from '../datapool/pools';
import { SelfieImageEntity, SelfieSettingEntity } from '../types';

export async function insertLog(item: Partial<SelfieImageEntity>) {
    let sqlInsert = 'insert into esigning_selfie_image(contract_number,image_id,token,status,response_code,response_message,created_date,updated_date,ip_address,latitude,longitude,location_name,location_response,map_image_id,id_card_image_id) values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) returning id';
    let poolWrite = dataPools.getPoolWrite();
    let currentDate = new Date();
    let result = await poolWrite.query(sqlInsert, [
        item.contract_number,
        item.image_id,
        item.token,
        item.status,
        item.response_code,
        item.response_message,
        currentDate,
        currentDate,
        item.ip_address,
        item.latitude,
        item.longitude,
        item.location_name,
        item.location_response,
        item.map_image_id,
        item.id_card_image_id,
    ]);
    return result?.rows[0];
}

export async function updateLog(item: Partial<SelfieImageEntity>) {
    let sqlInsert = 'update esigning_selfie_image set image_id = $1, response_code = $2, response_message = $3, map_image_id = $4, id_card_image_id = $5, status = $6, updated_date = $7 where id = $8';
    let poolWrite = dataPools.getPoolWrite();
    let currentDate = new Date();
    await poolWrite.query(sqlInsert, [
        item.image_id,
        item.response_code,
        item.response_message,
        item.map_image_id,
        item.id_card_image_id,
        item.status,
        currentDate,
        item.id
    ]);
}

export async function getRecentSelfie(contractNumber: string, limit: number) {
    let sql = 'select * from esigning_selfie_image where contract_number = $1 and response_code is not null and response_code <> -1 order by created_date desc limit $2';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<SelfieImageEntity>(sql, [contractNumber, limit]);
    return rows ?? [];
}

export async function getSelfieById(id: number) {
    let sql = 'select * from esigning_selfie_image where id = $1';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<SelfieImageEntity>(sql, [id]);
    return rows?.[0];
}

export async function getSelfieByToken(contractNumber: string, token: string) {
    let sql = 'select * from esigning_selfie_image where contract_number = $1 and token = $2 order by created_date desc';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<SelfieImageEntity>(sql, [contractNumber, token]);
    return rows ?? [];
}

export async function getSelfieByContract(contractNumber: string) {
    let sql = 'select * from esigning_selfie_image where contract_number = $1 and image_id is not null order by created_date desc';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<SelfieImageEntity>(sql, [contractNumber]);
    return rows ?? [];
}

export async function updateSelfieStatus(id: number, status: string) {
    let sql = 'update esigning_selfie_image set status = $1 where id = $2';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query<SelfieImageEntity>(sql, [status, id]);
}

export async function getSelfieSettings() {
    let sql = 'select * from esigning_selfie_setting where enabled != $1';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<SelfieSettingEntity>(sql, [0]);
    return rows ?? [];
}

export async function addSelfieSetting(partner_code: string, enabled: number) {
    let sql = 'insert into esigning_selfie_setting(partner_code,enabled,created_date,updated_date) values ($1,$2,$3,$4) on conflict (partner_code) do update set enabled = excluded.enabled, updated_date = excluded.updated_date';
    let poolWrite = dataPools.getPoolWrite();
    let date = new Date();
    await poolWrite.query<SelfieSettingEntity>(sql, [partner_code, enabled, date, date]);
}