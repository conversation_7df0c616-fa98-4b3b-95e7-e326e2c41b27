import * as dataPools from '../datapool/pools';
import { AuthorizeCounterSigningEntity } from '../types';

export async function insertAuthorizeCounterSigning(signedInfo: AuthorizeCounterSigningEntity) {
    let sqlInsert = 'insert into esigning_authorize_counter_signing(agreement_uuid, authorize_code, bill_code, contract_number, ec_signed_response_code, ec_signed_response_message, response_code, response_message) values ($1, $2, $3, $4, $5, $6, $7, $8)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlInsert, [signedInfo.agreement_uuid, signedInfo.authorize_code, signedInfo.bill_code, signedInfo.contract_number, signedInfo.ec_signed_response_code, signedInfo.ec_signed_response_message, signedInfo.response_code, signedInfo.response_message]);
}

export async function agreementUUIDSigned(agreementUUID: string) {
    let sqlSelect = 'select * from esigning_authorize_counter_signing where agreement_uuid = $1 and response_code = 0';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<AuthorizeCounterSigningEntity>(sqlSelect, [agreementUUID]);
    return resultData.rows?.[0];
}

export async function contractNumberSigned(contractNumber: string) {
    let sqlSelect = 'select * from esigning_authorize_counter_signing where contract_number = $1 and response_code = 0';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<AuthorizeCounterSigningEntity>(sqlSelect, [contractNumber]);
    return resultData.rows?.[0];
}
