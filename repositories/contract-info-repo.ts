import * as dataPools from '../datapool/pools';
import { ContractInfoEntity, UploadDocsEntity } from '../types';

export async function insertInfo(contractInfo: ContractInfoEntity, forceFlags: boolean): Promise<ContractInfoEntity> {
    let sqlInsert: string;
    if (forceFlags) {
        sqlInsert = `insert into esigning_contract_info(contract_number, customer_name, identity_card_id, phone_number, customer_address, state_province, partner_code, status_code, updated_date, flags, spic_path, request_id, pid_path, pid1_path) 
            values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) 
            on conflict (contract_number) 
            do update set customer_name = excluded.customer_name, identity_card_id = excluded.identity_card_id, phone_number = excluded.phone_number, customer_address = excluded.customer_address, state_province = excluded.state_province, partner_code = excluded.partner_code, status_code = excluded.status_code, updated_date = excluded.updated_date, spic_path = excluded.spic_path, request_id = excluded.request_id, pid_path = excluded.pid_path, pid1_path = excluded.pid1_path,
            flags = excluded.flags
            returning *`;
    } else {
        sqlInsert = `insert into esigning_contract_info(contract_number, customer_name, identity_card_id, phone_number, customer_address, state_province, partner_code, status_code, updated_date, flags, spic_path, request_id, pid_path, pid1_path) 
            values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) 
            on conflict (contract_number)
            do update set customer_name = excluded.customer_name, identity_card_id = excluded.identity_card_id, phone_number = excluded.phone_number, customer_address = excluded.customer_address, state_province = excluded.state_province, partner_code = excluded.partner_code, status_code = excluded.status_code, updated_date = excluded.updated_date, spic_path = excluded.spic_path, request_id = excluded.request_id, pid_path = excluded.pid_path, pid1_path = excluded.pid1_path,
            flags = case when esigning_contract_info.flags is null then excluded.flags else esigning_contract_info.flags end 
            returning *`;
    }
    let poolWrite = dataPools.getPoolWrite();
    let currentDate = new Date();
    let result = await poolWrite.query<ContractInfoEntity>(sqlInsert, [contractInfo.contract_number, contractInfo.customer_name, contractInfo.identity_card_id, contractInfo.phone_number, contractInfo.customer_address, contractInfo.state_province, contractInfo.partner_code, contractInfo.status_code, currentDate, JSON.stringify(contractInfo.flags), contractInfo.spic_path, contractInfo.request_id, contractInfo.pid_path, contractInfo.pid1_path]);
    let contractInfoOutput = result?.rows?.[0];
    if (contractInfoOutput) {
        return {
            ...contractInfoOutput,
            flags: JSON.parse((contractInfoOutput["flags"] as string) ?? "{}"),
        }
    }
}

export async function findByContractNumber(contractNumber: string): Promise<ContractInfoEntity> {
    let sqlSelect = 'select * from esigning_contract_info where contract_number = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<ContractInfoEntity>(sqlSelect, [contractNumber]);
    let contractInfoOutput = resultData?.rows?.[0];
    if (contractInfoOutput) {
        return {
            ...contractInfoOutput,
            flags: JSON.parse((contractInfoOutput["flags"] as string) ?? "{}"),
        }
    }
}
export async function saveListDocs(contract_number: string, response: string, status: number) {
    let sqlInsert = 'insert into esigning_upload_docs(contract_number, response, status) values ($1, $2, $3)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlInsert, [contract_number, response, status]);
}
export async function getListDocs(contractNumber: string) {
    let sqlSelect = 'select * from esigning_upload_docs where contract_number = $1 and status = 1 order by created_date desc';
    let poolWrite = dataPools.getPoolWrite();
    let resultData = await poolWrite.query<UploadDocsEntity>(sqlSelect, [contractNumber]);
    return resultData;
}
export async function updateListDocs(contractNumber: string) {
    let sqlSelect = `update esigning_upload_docs set status = 2 where contract_number = $1`;
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query(sqlSelect, [contractNumber]);
    return resultData;
}