import * as dataPools from '../datapool/pools';
import moment from 'moment';
import * as configService from '../services/config-service';
import { RegenerateAuthorizationCodeEntity } from '../types';

export async function insertInfo(authorizationCodeInfo: RegenerateAuthorizationCodeEntity) {
    let currentTimeStamp = new Date().getTime();
    let timeMillisecondsToExpire = 5 * 60 * 1000; // 5 min
    let expireDate = new Date(currentTimeStamp + timeMillisecondsToExpire);
    let sqlInsert = 'insert into esigning_regenerate_authorization_code(agreement_uuid, authorize_code, contract_number, response_code, response_message, expire_date) values ($1, $2, $3, $4, $5, $6)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlInsert, [authorizationCodeInfo.agreement_uuid, authorizationCodeInfo.authorize_code, authorizationCodeInfo.contract_number, authorizationCodeInfo.response_code, authorizationCodeInfo.response_message, expireDate]);
}

export async function findByAgreementUUID(agreementUUID: string) {
    let sqlSelect = 'select * from esigning_regenerate_authorization_code where agreement_uuid = $1 order by created_date desc';
    let poolRead = dataPools.getPoolWrite();
    let resultData = poolRead.query<RegenerateAuthorizationCodeEntity>(sqlSelect, [agreementUUID]);
    return resultData;
}
export async function findOtpDue(contract_number: string) {
    let cloudConfig = configService.getConfig();
    let timeResend = cloudConfig.data.fptEsignApi.timeResend ?? 120;
    timeResend = 5 * 60 - timeResend;
    let expireDate = moment().add(timeResend, 'seconds').format('YYYY-MM-DD HH:mm:ss');;
    let sqlInsert = `select * from esigning_regenerate_authorization_code where contract_number = $1 and expire_date > $2`;
    let poolWrite = dataPools.getPoolWrite();
    return await poolWrite.query<RegenerateAuthorizationCodeEntity>(sqlInsert, [contract_number, expireDate]);
}
export async function findOptSend(contract_number: string) {
    let cloudConfig = configService.getConfig();
    let timeLock = 0 - cloudConfig.data.fptEsignApi.timeLock;
    let expireDate = moment().add(timeLock, 'hours').format('YYYY-MM-DD HH:mm:ss');
    let sqlSelect = `select * from esigning_regenerate_authorization_code where contract_number = $1 and response_code = '0' and expire_date > $2`;
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<RegenerateAuthorizationCodeEntity>(sqlSelect, [contract_number, expireDate]);
    return resultData;
}
/**
 * Lock gui SMS trong 1 khoang thoi gian, agreementUUID = contractNumber
 */
export async function insertLock(agreementUUID: string, contractNumber: string) {
    let cloudConfig = configService.getConfig();
    let timeLock = cloudConfig.data.fptEsignApi.timeLock;
    let expireDate = moment().add(timeLock, 'hours').format('YYYY-MM-DD HH:mm:ss');
    let sqlInsert = 'insert into esigning_regenerate_authorization_code(agreement_uuid, authorize_code, contract_number, response_code, response_message, expire_date) values ($1, $2, $3, $4, $5, $6)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlInsert, [agreementUUID, 'LOCK', contractNumber, 0, 'LOCK_24H', expireDate]);
}
export async function getLockContract(contractNumber: string) {
    let expireDate = moment().format('YYYY-MM-DD HH:mm:ss');
    let sqlInsert = `select * from esigning_regenerate_authorization_code where contract_number = $1 and authorize_code = 'LOCK' and expire_date > $2`;
    let poolWrite = dataPools.getPoolWrite();
    return await poolWrite.query<RegenerateAuthorizationCodeEntity>(sqlInsert, [contractNumber, expireDate]);
}