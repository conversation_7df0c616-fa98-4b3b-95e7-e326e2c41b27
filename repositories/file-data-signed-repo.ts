import * as dataPools from '../datapool/pools';
import { FileDataSignedEntity } from '../types';

export async function insertFileDataSigned(fileDataInfo: FileDataSignedEntity) {
    let sqlInsert = 'insert into esigning_file_data_signed(agreement_uuid, contract_number, file_name, signed_file_data) values ($1, $2, $3, $4)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlInsert, [fileDataInfo.agreement_uuid, fileDataInfo.contract_number, fileDataInfo.file_name, fileDataInfo.signed_file_data]);
}

export async function findByAgreementUuidAndContractNumber(agreementUUID: string, contractNumber: string): Promise<FileDataSignedEntity> {
    let sqlSelect = 'select * from esigning_file_data_signed where agreement_uuid = $1 and contract_number = $2';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<FileDataSignedEntity>(sqlSelect, [agreementUUID, contractNumber]);
    return resultData.rows?.[0];
}

export async function findByContractAnnexNumber(contractAnnexNumber: string): Promise<FileDataSignedEntity> {
    let sqlSelect = 'select * from esigning_file_data_signed where contract_number = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<FileDataSignedEntity>(sqlSelect, [contractAnnexNumber]);
    return resultData.rows?.[0];
}

export async function findS3ByContractNumber(contractNumber: string): Promise<string> {
    let poolRead = dataPools.getPoolWrite();
    let sqlSelect = 'select s3_key from esigning_file_data_signed where contract_number = $1 and s3_key is not null';
    let resultData = await poolRead.query(sqlSelect, [contractNumber]);
    return resultData.rows?.[0]?.s3_key;
}

export async function findByContractNumber(contractNumber: string): Promise<FileDataSignedEntity> {
    let poolRead = dataPools.getPoolWrite();
    let sqlSelect = 'select * from esigning_file_data_signed where contract_number = $1';
    let resultData = await poolRead.query(sqlSelect, [contractNumber]);
    return resultData.rows?.[0];
}

export async function updateS3Info(contractNumber: string, s3Key: string) {
    if (s3Key) {
        let sql = 'update esigning_file_data_signed set s3_key = $1, signed_file_data = null where contract_number = $2';
        let poolWrite = dataPools.getPoolWrite();
        await poolWrite.query(sql, [s3Key, contractNumber]);
    }
}
