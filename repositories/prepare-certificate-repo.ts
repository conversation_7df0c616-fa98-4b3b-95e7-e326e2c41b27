import * as dataPools from '../datapool/pools';
import { ContractInfoEntity, FptResponse, PrepareCertificateInfoEntity } from '../types';

export async function insertPrepareCertificate(agreementUUID: string, contractInfoEntity: ContractInfoEntity, fptResponse: FptResponse) {
    let dateNow = new Date();
    let sqlQuery = 'insert into esigning_prepare_certificate_info(contract_number, updated_date, country, current_agreement_uuid, agreement_uuid_created_date, location, mobile_no, personal_id, personal_name, state_province, response_code, response_message) values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) on conflict (contract_number) do update set updated_date = excluded.updated_date, country = excluded.country, current_agreement_uuid = excluded.current_agreement_uuid, agreement_uuid_created_date = excluded.agreement_uuid_created_date, location = excluded.location, mobile_no = excluded.mobile_no, personal_id = excluded.personal_id, personal_name = excluded.personal_name, state_province = excluded.state_province, response_code = excluded.response_code, response_message = excluded.response_message';
    let sqlInsertHistory = 'insert into esigning_agreement_uuid_created_history(agreement_uuid, response_code, response_message, contract_number) values ($1, $2, $3, $4)';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlQuery, [contractInfoEntity.contract_number, dateNow, contractInfoEntity.country, agreementUUID, dateNow, contractInfoEntity.customer_address, contractInfoEntity.phone_number, contractInfoEntity.identity_card_id, contractInfoEntity.customer_name, contractInfoEntity.state_province, fptResponse.responseCode, fptResponse.responseMessage]);
    await poolWrite.query(sqlInsertHistory, [agreementUUID, fptResponse.responseCode, fptResponse.responseMessage, contractInfoEntity.contract_number]);
}

export async function findByContractNumber(contractNumber: string) {
    let sqlSelect = 'select * from esigning_prepare_certificate_info where contract_number = $1';
    let poolWrite = dataPools.getPoolWrite();
    let resultData = await poolWrite.query<PrepareCertificateInfoEntity>(sqlSelect, [contractNumber]);
    return resultData;
}