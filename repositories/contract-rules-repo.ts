import * as dataPools from '../datapool/pools';
import { ContractRuleChosenItemRequest, ContractRuleChosenListEntity, ContractRuleInfoEntity } from '../types';
import * as common from "../utils/common";

export async function getListContractRule(serviceName: string) {
    let sqlQuery = 'select * from esigning_contract_rule_info where is_deleted = false and service = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<ContractRuleInfoEntity>(sqlQuery, [serviceName]);
    return resultData;
}

export async function getListContractRuleChosen(contractNumber: string) {
    let sqlQuery = 'select * from esigning_contract_rule_info as cri inner join esigning_contract_rule_chosen_list crcl on cast(cri.rule_id as varchar) = crcl.rule_id where crcl.contract_number = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<ContractRuleInfoEntity & ContractRuleChosenListEntity>(sqlQuery, [contractNumber]);
    return resultData;
}

export async function updateContractRuleChosenList(contractNumber: string, contractRuleChosenList: ContractRuleChosenItemRequest[]) {
    let sqlQuery = 'update esigning_contract_rule_chosen_list set agreed = $1 where contract_number = $2 and rule_id = $3';
    let poolWrite = dataPools.getPoolWrite();
    for (const element of contractRuleChosenList) {
        try {
            await poolWrite.query(sqlQuery, [element.agreed, contractNumber, element.ruleId]);
        } catch (error) {
            common.error(error);
        }
    }
    return true;
}

export async function insertContractRuleChosenList(contractNumber: string, contractRuleChosenList: ContractRuleChosenItemRequest[]) {
    let sqlQuery = 'insert into esigning_contract_rule_chosen_list(contract_number, rule_id, agreed) values ($1, $2, $3)';
    let poolWrite = dataPools.getPoolWrite();
    for (const element of contractRuleChosenList) {
        try {
            await poolWrite.query(sqlQuery, [contractNumber, element.ruleId, element.agreed]);
        } catch (error) {
            common.error(error);
        }
    }
    return true;
}
