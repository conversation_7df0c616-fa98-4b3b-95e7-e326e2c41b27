import * as dataPools from '../datapool/pools';
import { ContractAnnexInfoEntity } from '../types';

export async function insertInfo(info: ContractAnnexInfoEntity) {
    let sqlInsert = 'insert into esigning_contract_annex_info (annex_number,product_name,product_code,contract_number,contract_date,customer_name,birthday,id_number,issue_date,issue_place,current_address,current_ward,current_district,current_province,permanent_address,permanent_ward,permanent_district,permanent_province,phone_number,email,request_name,request_date,restructuring_time,overdue_interest,ri_rate_month,ri_rate_year,gpp,gpp_month,gpp_rate_month,gpp_rate_year,gpi,gpi_month,gpi_rate_month,gpi_rate_year,status_code,updated_date,maturity_date,prin_debt)' +
        ' values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30,$31,$32,$33,$34,$35,$36,$37,$38)' +
        ' on conflict (annex_number) do update set product_name = excluded.product_name, product_code = excluded.product_code, contract_number = excluded.contract_number, contract_date = excluded.contract_date, customer_name = excluded.customer_name, birthday = excluded.birthday, id_number = excluded.id_number, issue_date = excluded.issue_date, issue_place = excluded.issue_place, current_address = excluded.current_address, current_ward = excluded.current_ward, current_district = excluded.current_district, current_province = excluded.current_province, permanent_address = excluded.permanent_address, permanent_ward = excluded.permanent_ward, permanent_district = excluded.permanent_district, permanent_province = excluded.permanent_province, phone_number = excluded.phone_number, email = excluded.email, request_name = excluded.request_name, request_date = excluded.request_date, restructuring_time = excluded.restructuring_time, overdue_interest = excluded.overdue_interest, ri_rate_month = excluded.ri_rate_month, ri_rate_year = excluded.ri_rate_year, gpp = excluded.gpp, gpp_month = excluded.gpp_month, gpp_rate_month = excluded.gpp_rate_month, gpp_rate_year = excluded.gpp_rate_year, gpi = excluded.gpi, gpi_month = excluded.gpi_month, gpi_rate_month = excluded.gpi_rate_month, gpi_rate_year = excluded.gpi_rate_year, status_code = excluded.status_code, updated_date = excluded.updated_date, maturity_date = excluded.maturity_date, prin_debt = excluded.prin_debt';
    let poolWrite = dataPools.getPoolWrite();
    let currentDate = new Date();
    await poolWrite.query(sqlInsert, [info.annex_number, info.product_name, info.product_code, info.contract_number, info.contract_date, info.customer_name, info.birthday, info.id_number, info.issue_date, info.issue_place, info.current_address, info.current_ward, info.current_district, info.current_province, info.permanent_address, info.permanent_ward, info.permanent_district, info.permanent_province, info.phone_number, info.email, info.request_name, info.request_date, info.restructuring_time, info.overdue_interest, info.ri_rate_month, info.ri_rate_year, info.gpp, info.gpp_month, info.gpp_rate_month, info.gpp_rate_year, info.gpi, info.gpi_month, info.gpi_rate_month, info.gpi_rate_year, info.status_code, currentDate, info.maturity_date, info.prin_debt]);
}

export async function findByContractNumber(contractNumber: string) {
    let sqlSelect = 'select * from esigning_contract_annex_info where contract_number = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<ContractAnnexInfoEntity>(sqlSelect, [contractNumber]);
    return resultData.rows?.[0];
}

export async function updateSignedDate(contractNumber: string, date: Date) {
    let sqlUpdate = 'update esigning_contract_annex_info set signed_date = $1 where contract_number = $2 and status_code = $3';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlUpdate, [date, contractNumber, "SIGNING"]);
}

export async function signAnnex(contractNumber: string) {
    let sqlUpdate = 'update esigning_contract_annex_info set status_code = $1 where contract_number = $2';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlUpdate, ["SIGNED", contractNumber]);
}

export async function signSchedule(contractNumber: string) {
    let sqlUpdate = 'update esigning_contract_annex_info set status_code = $1 where contract_number = $2';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlUpdate, ["FULL_SIGNED", contractNumber]);
}