import * as dataPools from '../datapool/pools';
import { TermsConditionsAgreedListEntity, TermsConditionsAgreeInfoEntity } from '../types';
import * as common from "../utils/common";

export async function getListTermsConditions() {
    let sqlQuery = 'select * from esigning_terms_conditions_agree_info where is_deleted = false';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<TermsConditionsAgreeInfoEntity>(sqlQuery);
    return resultData;
}

export async function getListTermsConditionsAgreed(contractNumber: string) {
    let sqlQuery = 'select * from esigning_terms_conditions_agree_info as tca inner join esigning_terms_conditions_agreed_list tcal on cast(tca.term_id as varchar) = tcal.term_id where tcal.contract_number = $1';
    let poolRead = dataPools.getPoolWrite();
    let resultData = await poolRead.query<TermsConditionsAgreeInfoEntity & TermsConditionsAgreedListEntity>(sqlQuery, [contractNumber]);
    return resultData;
}

export async function insertTermsConditionsAgreed(contractNumber: string, termsAndConditions: { termId: string, agreed: boolean }[]) {
    var sqlQuery = 'insert into esigning_terms_conditions_agreed_list(contract_number, term_id, agreed) values ($1, $2, $3)';
    let poolWrite = dataPools.getPoolWrite();
    for (const element of termsAndConditions) {
        try {
            await poolWrite.query(sqlQuery, [contractNumber, element.termId, element.agreed]);
        } catch (error) {
            common.error(error);
        }
    }
}