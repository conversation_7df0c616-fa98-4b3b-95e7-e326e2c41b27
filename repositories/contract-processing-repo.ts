import * as dataPools from '../datapool/pools';
import { ContractProcessingEntity } from '../types';

export async function insertProcessing(contractNumber: string) {
    let sqlInsert = 'insert into esigning_contract_processing(contract_number, created_date, updated_date) values ($1, $2, $3)';
    let poolWrite = dataPools.getPoolWrite();
    let currentDate = new Date();
    await poolWrite.query(sqlInsert, [contractNumber, currentDate, currentDate]);
}

export async function getContractProcessing(contractNumber: string) {
    let sqlCommand = 'select * from esigning_contract_processing where contract_number = $1';
    let poolWrite = dataPools.getPoolWrite();
    let results = await poolWrite.query<ContractProcessingEntity>(sqlCommand, [contractNumber]);
    return results.rows?.[0];
}

export async function updateProcessing(contractNumber: string, status: string, message: string, responseUpload?: any) {
    let sqlCommand = 'update esigning_contract_processing set status = $1, message = $2, response_upload = $3 where contract_number = $4';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlCommand, [status, message, JSON.stringify(responseUpload), contractNumber]);
}

export async function deleteProcessing(id: number) {
    let sqlCommand = 'delete from esigning_contract_processing where id = $1';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query(sqlCommand, [id]);
}