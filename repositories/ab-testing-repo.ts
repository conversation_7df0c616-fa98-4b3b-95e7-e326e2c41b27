import * as dataPools from '../datapool/pools';
import { AbTestingConfig } from '../types';

export async function getAbTestingConfigs(): Promise<AbTestingConfig[]> {
    let sql = 'select * from esigning_ab_testing where is_delete != $1';
    let poolWrite = dataPools.getPoolWrite();
    let { rows } = await poolWrite.query<AbTestingConfig>(sql, [1]);
    return (rows ?? []).map(x => ({
        ...x,
        branches: JSON.parse(x.data ?? "{}")
    }));
}

export async function addAbTestingConfig(config: Partial<AbTestingConfig>) {
    let sql = 'insert into esigning_ab_testing (partner_code,data,start_time,end_time,is_delete) values ($1,$2,$3,$4,$5)';
    let poolWrite = dataPools.getPoolWrite();
    let date = new Date();
    await poolWrite.query<AbTestingConfig>(sql, [config.partner_code, JSON.stringify(config.branches), config.start_time, config.end_time, config.is_delete ? 1 : 0]);
}

export async function updateAbTestingConfig(config: Partial<AbTestingConfig>) {
    let sql = 'update esigning_ab_testing set data = $1, start_time = $2, end_time = $3, is_delete = $4 where id = $5';
    let poolWrite = dataPools.getPoolWrite();
    await poolWrite.query<AbTestingConfig>(sql, [JSON.stringify(config.branches), config.start_time, config.end_time, config.is_delete ? 1 : 0, config.id]);
}