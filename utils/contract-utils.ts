import { CancelLoanContractLosResponse, ContractInfoLosResponse, ContractRuleItem, ContractSystemType, RefuseLoanContractLosResponse, VerifyLoanLosResponse } from "../types";
import * as contractService from '../services/contract-service';
import { convertServiceApi, losApi, losClApi, losMcApi, losUnitedApi, losVfApi } from "../api";

export const DECISION_V02_RES_DECISION = {
    APPROVE: 'APPROVE',
    REJECT: 'REJECT',
    MANUAL_DECISION: 'MANUAL',
    CANCEL: 'CANCEL',
    WAIT_CIC: 'WAIT_CIC'
}
export function getContractSystemType(contractNumber: string): ContractSystemType | undefined {
    if (!contractNumber)
        return undefined;
    if (/^(CREDIT|11)/.test(contractNumber))
        return "credit";
    if (/^(MER|KU|MC|2)/.test(contractNumber))
        return "merchant";
    if (/^4/.test(contractNumber))
        return "vinfast";
    if (/^5/.test(contractNumber))
        return "cashloan";
    if (/^6/.test(contractNumber))
        return "united";
}

export async function getContractInfo(contractNumber: string) {
    let resultContractInfo: ContractInfoLosResponse;
    const systemType = getContractSystemType(contractNumber);
    if (systemType === "credit") {
        resultContractInfo = await losApi.contractInfo(contractNumber);
    } else if (systemType === "merchant") {
        resultContractInfo = await losMcApi.contractInfo(contractNumber);
    } else if (systemType === "vinfast") {
        resultContractInfo = await losVfApi.contractInfo(contractNumber);
    } else if (systemType === "cashloan") {
        resultContractInfo = await losClApi.contractInfo(contractNumber);
    } else if (systemType === "united") {
        resultContractInfo = await losUnitedApi.contractInfo(contractNumber);
    }
    return resultContractInfo;
}

export async function verifyLoan(contractNumber: string) {
    let result: VerifyLoanLosResponse;
    const systemType = getContractSystemType(contractNumber);
    if (systemType === "united") {
        result = await losUnitedApi.verifyLoan(contractNumber);
    }
    return result;
}

export async function callbackUpdateStatusLos(contractNumber: string, decisionCode: string) {
    if (decisionCode == DECISION_V02_RES_DECISION.CANCEL) {
        return cancelLoanContract(contractNumber, decisionCode)
    } else if (decisionCode == DECISION_V02_RES_DECISION.REJECT) {
        return refuseLoanContract(contractNumber, decisionCode)
    }
}

export async function cancelLoanContract(contractNumber: string, decisionCode: string) {
    let result: CancelLoanContractLosResponse;
    const systemType = getContractSystemType(contractNumber);
    if (systemType === "united") {
        result = await losUnitedApi.cancelLoanContract(contractNumber, decisionCode);
    }
    return result;
}

export async function refuseLoanContract(contractNumber: string, decisionCode: string) {
    let result: RefuseLoanContractLosResponse;
    const systemType = getContractSystemType(contractNumber);
    if (systemType === "united") {
        result = await losUnitedApi.refuseLoanContract(contractNumber, decisionCode);
    }
    return result;
}

export async function getContractFile(contractNumber: string, contractRuleList?: ContractRuleItem[], newFileHost?: boolean): Promise<any> {
    let contractDetailResponse;
    const systemType = getContractSystemType(contractNumber);
    if (newFileHost) {
        contractDetailResponse = await convertServiceApi.downloadFile(contractNumber);
    } else if (systemType === "credit") {
        contractDetailResponse = await losApi.contractFile(contractNumber);
    } else if (systemType === "merchant") {
        contractDetailResponse = await losMcApi.contractFile(contractNumber);
    } else if (systemType === "vinfast") {
        contractDetailResponse = await losVfApi.contractFile(contractNumber);
    } else if (systemType === "cashloan") {
        //contractDetailResponse = await losCLServiceApi.contractFile(contractNumber);
        let contractInfo = await losClApi.contractInfo(contractNumber);
        let contractType = parseInt(contractInfo?.data?.contractType?.toString());
        if (contractType == 2) {
            contractDetailResponse = await losClApi.contractFile(contractNumber);
        } else {
            let requestRule: Record<string, string | boolean> = {
                contract_number: contractNumber
            }
            if (!contractRuleList)
                contractRuleList = await contractService.getListContractRuleChosen(contractNumber);
            for (let rule of contractRuleList) {
                let selection = rule?.keySelection;
                let agree = rule?.defaultAgree;
                if (selection)
                    requestRule[selection] = agree;
            }
            contractDetailResponse = await losClApi.contractFileV2(requestRule);
        }
    } else if (systemType === "united") {
        //contractDetailResponse = await losCLServiceApi.contractFile(contractNumber);
        let contractInfo = await losUnitedApi.contractInfo(contractNumber);
        let contractType = parseInt(contractInfo?.data?.contractType?.toString());
        if (contractType == 2) {
            contractDetailResponse = await losUnitedApi.contractFile(contractNumber);
        } else {
            let requestRule: Record<string, string | boolean> = {
                contract_number: contractNumber
            }
            if (!contractRuleList)
                contractRuleList = await contractService.getListContractRuleChosen(contractNumber);
            for (let rule of contractRuleList) {
                let selection = rule?.keySelection;
                let agree = rule?.defaultAgree;
                if (selection)
                    requestRule[selection] = agree;
            }
            contractDetailResponse = await losUnitedApi.contractFileV2(requestRule);
        }
    }
    return contractDetailResponse;
}

export async function getDbrContractFiles(contractNumber: string) {
    let response = await losUnitedApi.dbrContractFiles(contractNumber);
    return response;
}

export async function getDbrContractInfo(contractNumber: string) {
    let response = await losUnitedApi.dbrContractInfo(contractNumber);
    return response;
}

export async function uploadSignedDbrFiles(contractNumber: string, signedAnnex, signedSchedule) {
    await losUnitedApi.uploadSignedFile(contractNumber, signedAnnex, signedSchedule);
}