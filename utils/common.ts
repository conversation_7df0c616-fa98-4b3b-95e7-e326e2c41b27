/**
 * Thu vien su dung cac ham de goi API, lay configuration, ghi log, push notification
 */
import request, { CoreOptions, RequiredUriUrl } from "request";
import crypto from "crypto";
import fs from "fs";
import forge from "node-forge";
import { BasicService } from "../services/config-service";
import httpContext from "express-http-context";
import moment from "moment";
import axios from "axios";

let moduleName: string;
/**
 * Ham set ten service cho common.
 * @param {*} name
 */
export function setServiceName(name: string) {
  moduleName = name;
}
/**
 * Ham ghi log
 */
export function log(obj: any, title?: string) {
  let str: string;
  let token = httpContext.get("requestId");
  token = token ? token + "| " : "";
  title = title ? title + "| " : "";
  if (typeof obj === "string") {
    str = token + title + (obj || "").replace("\n", " ");
  } else {
    str = token + title + JSON.stringify(obj);
  }
  if (!process.env.NODE_ENV) {
    if (title) console.log(title, obj);
    else console.log(obj);
  } else {
    console.log(str);
  }
}
export function error(error: Error) {
  let level = "error";
  let token = httpContext.get("requestId");
  token = token ? token + "| " : "";
  let str =
    token +
    level +
    "| " +
    JSON.stringify(error, Object.getOwnPropertyNames(error));
  if (!process.env.NODE_ENV) {
    console.log(error);
  } else {
    console.log(str);
  }
}
export function getAPI<T = any>(
  link: string,
  header: Record<string, string | number>
): Promise<T> {
  log("getAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      let reqObj: CoreOptions & RequiredUriUrl = {
        url: link,
        method: "GET",
      };

      if (header != undefined) {
        reqObj.headers = header;
      }
      request(reqObj, (error, response1, body) => {
        if (error) {
          reject(error);
        } else {
          try {
            let obj = JSON.parse(body);
            resolve(obj);
          } catch (error) {
            reject(error);
          }
        }
      });
    } catch (e) {
      reject(e);
    }
  });
}
export function getFileAPI<T = any>(
  link: string,
  header: Record<string, string | number>
): Promise<T> {
  log("getAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      let reqObj: CoreOptions & RequiredUriUrl = {
        url: link,
        method: "GET",
      };

      if (header != undefined) {
        reqObj.headers = header;
      }
      request(reqObj, (error, response1, body) => {
        if (error) {
          reject(error);
        } else {
          resolve(body);
        }
      });
    } catch (e) {
      reject(e);
    }
  });
}
export function postAPI<T = any>(
  link: string,
  json,
  header: Record<string, string | number>
): Promise<T> {
  log("postAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      let reqObj: CoreOptions & RequiredUriUrl = {
        url: link,
        method: "POST",
      };

      if (json != undefined) {
        reqObj.json = json;
      }
      if (header != undefined) {
        reqObj.headers = header;
      }
      request(reqObj, (error, response, body) => {
        if (error) {
          reject(error);
        } else {
          resolve(body);
        }
      });
    } catch (e) {
      console.log("ERROR", e);
      reject(e);
    }
  });
}

export function postAPIv2<T = any>(link: string, body, header): Promise<T> {
  log("postAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: link,
        headers: header,
        data: JSON.stringify(body),
      };

      axios
        .request(config)
        .then((response) => {
          console.log(JSON.stringify(response.data));
          resolve(response.data);
        })
        .catch((error) => {
          reject(error);
        });
    } catch (e) {
      console.log("ERROR", e);
      reject(e);
    }
  });
}

export function updateAPI<T = any>(
  link: string,
  json,
  header: Record<string, string | number>
): Promise<T> {
  log("updateAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      try {
        let reqObj: CoreOptions & RequiredUriUrl = {
          url: link,
          method: "UPDATE",
        };

        if (json != undefined) {
          reqObj.json = json;
        }
        if (header != undefined) {
          reqObj.headers = header;
        }
        request(reqObj, (error, response1, body) => {
          if (error) {
            reject(error);
          } else {
            resolve(body);
          }
        });
      } catch (e) {
        reject(e);
      }
    } catch (e) {
      reject(e);
    }
  });
}
export function deleteAPI<T = any>(
  link: string,
  json,
  header: Record<string, string | number>
): Promise<T> {
  log("deleteAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      let reqObj: CoreOptions & RequiredUriUrl = {
        url: link,
        method: "DELETE",
      };

      if (json != undefined) {
        reqObj.json = json;
      }
      if (header != undefined) {
        reqObj.headers = header;
      }
      request(reqObj, (error, response1, body) => {
        if (error) {
          reject(error);
        } else {
          resolve(body);
        }
      });
    } catch (e) {
      reject(e);
    }
  });
}

export function uploadFileFromDataAPI<T = any>(
  link: string,
  formData,
  header: Record<string, string | number>
): Promise<T> {
  log("uploadFileFromDataAPI link: " + link);
  return new Promise(function (resolve, reject) {
    try {
      let reqObj: CoreOptions & RequiredUriUrl = {
        url: link,
        method: "POST",
      };

      if (formData != undefined) {
        reqObj.formData = formData;
      }
      if (header != undefined) {
        reqObj.headers = header;
      }
      request(reqObj, (error, response, body) => {
        if (error) {
          reject(error);
        } else {
          resolve(body);
        }
      });
    } catch (e) {
      reject(e);
    }
  });
}

export function getPKCS1Signature(
  data,
  relyingPartyKeyStore: string,
  relyingPartyKeyStorePassword
) {
  let keyFile = fs.readFileSync(relyingPartyKeyStore);
  let keyBase64 = keyFile.toString("base64");
  let p12Der = forge.util.decode64(keyBase64);
  let p12Asn1 = forge.asn1.fromDer(p12Der);
  let p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, relyingPartyKeyStorePassword);

  // get bags by type
  var certBags = p12.getBags({ bagType: forge.pki.oids.certBag });
  var pkeyBags = p12.getBags({ bagType: forge.pki.oids.pkcs8ShroudedKeyBag });
  // fetching certBag
  var certBag = certBags[forge.pki.oids.certBag][0];
  // fetching keyBag
  var keybag = pkeyBags[forge.pki.oids.pkcs8ShroudedKeyBag][0];
  // generate pem from private key
  var privateKeyPem = forge.pki.privateKeyToPem(keybag.key);

  const sign = crypto.createSign("RSA-SHA1");
  sign.update(data);
  const result = sign.sign(privateKeyPem);
  log("private key");
  log(result.toString("base64"));
  return result.toString("base64");
}

export function numDayBetween(inputDate: Date, currentDate: Date): number {
  let diff = currentDate.getTime() - inputDate.getTime();
  let diffDays = diff / (24 * 60 * 60 * 1000);
  return diffDays;
}

export function writeFileFromBuffer(fileData) {
  let path = "contract.pdf";
  let buffer = Buffer.from(fileData);

  // open the file in writing mode, adding a callback function where we do the actual writing
  fs.open(path, "w", function (err, fd) {
    if (err) {
      throw "could not open file: " + err;
    }

    // write the contents of the buffer, from position 0 to the end, to the file descriptor returned in opening our file
    fs.write(fd, buffer, 0, buffer.length, null, function (err) {
      if (err) throw "error writing file: " + err;
      fs.close(fd, function () {
        console.log("wrote the file successfully");
      });
    });
  });
}

export function arrayBufferToBase64(buffer: Buffer) {
  let binary = "";
  var bytes = new Uint8Array(buffer);
  var len = bytes.byteLength;
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return binary;
}

export function getServiceUrl(serviceObj: BasicService) {
  if (process.env.HOST_ENV === "localhost") return serviceObj?.localhost;
  else return serviceObj?.internalLb;
}

export function randomInt(max: number) {
  const buf = crypto.randomBytes(1);
  return Math.floor((buf.at(0) / 256) * max);
}
