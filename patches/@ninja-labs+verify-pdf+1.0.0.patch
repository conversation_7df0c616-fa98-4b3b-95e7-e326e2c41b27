diff --git a/node_modules/@ninja-labs/verify-pdf/lib/helpers/extractSignature.js b/node_modules/@ninja-labs/verify-pdf/lib/helpers/extractSignature.js
index 60acec6..f69cc2b 100644
--- a/node_modules/@ninja-labs/verify-pdf/lib/helpers/extractSignature.js
+++ b/node_modules/@ninja-labs/verify-pdf/lib/helpers/extractSignature.js
@@ -32,22 +32,23 @@ const extractSignature = (pdf) => {
   const lastIndex = byteRanges.length - 1;
   const endOfByteRange = byteRanges[lastIndex][2] + byteRanges[lastIndex][3];
 
-  if (pdfBuffer.length > endOfByteRange) {
-    throw new VerifyPDFError(
-      'Failed byte range verification.',
-      VerifyPDFError.VERIFY_BYTE_RANGE,
-    );
-  }
+  // if (pdfBuffer.length > endOfByteRange) {
+  //   throw new VerifyPDFError(
+  //     'Failed byte range verification.',
+  //     VerifyPDFError.VERIFY_BYTE_RANGE,
+  //   );
+  // }
 
   const signatureStr = [];
   const signedData = [];
+  const bias = pdf.toString().indexOf("%PDF");
   byteRanges.forEach((byteRange) => {
     signedData.push(Buffer.concat([
-      pdfBuffer.slice(byteRange[0], byteRange[0] + byteRange[1]),
-      pdfBuffer.slice(byteRange[2], byteRange[2] + byteRange[3]),
+      pdfBuffer.slice(bias + byteRange[0], bias + byteRange[0] + byteRange[1]),
+      pdfBuffer.slice(bias + byteRange[2], bias + byteRange[2] + byteRange[3]),
     ]));
 
-    const signatureHex = pdfBuffer.slice(byteRange[0] + byteRange[1] + 1, byteRange[2]).toString('latin1');
+    const signatureHex = pdfBuffer.slice(bias + byteRange[0] + byteRange[1] + 1, bias + byteRange[2]).toString('latin1');
     signatureStr.push(Buffer.from(signatureHex, 'hex').toString('latin1'));
   });
 
