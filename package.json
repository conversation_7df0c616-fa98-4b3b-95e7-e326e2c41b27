{"name": "esigning-service", "version": "1.0.0", "description": "Rest full API for Esigning-Service", "main": "app.js", "scripts": {"start-dev": "cross-env NODE_ENV=dev node dist/app.js", "start-uat": "cross-env NODE_ENV=uat node dist/app.js", "start-prod": "cross-env NODE_ENV=prod node dist/app.js", "build": "tsc", "start": "cross-env DEBUG=true NODE_ENV=uat ts-node -T app.ts", "postinstall": "patch-package"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ninja-labs/verify-pdf": "^1.0.0", "aws-sdk": "^2.1086.0", "axios": "^1.6.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "dateformat": "^4.5.1", "docxtemplater": "^3.31.5", "dotenv": "^8.2.0", "exceljs": "^4.3.0", "express": "^4.17.1", "express-fileupload": "^1.4.0", "express-http-context": "^1.2.4", "file-type": "^16.5.4", "got": "^12.1.0", "image-size": "^1.0.0", "jimp": "^0.16.2", "libreoffice-convert": "^1.3.2", "moment": "^2.30.1", "nanoid": "^3.1.30", "node-cache": "^5.1.2", "node-cron": "^3.0.0", "node-fetch": "^2.6.5", "patch-package": "^6.4.7", "pdf-lib": "^1.16.0", "pdfjs-dist": "^2.14.305", "pg": "^8.5.1", "pizzip": "^3.1.1", "request": "^2.88.2", "request-ip": "^3.3.0", "typescript": "^4.4.3", "uuid": "^8.3.2"}, "devDependencies": {"@types/cors": "^2.8.12", "@types/dateformat": "^3.0.1", "@types/express": "^4.17.13", "@types/express-fileupload": "^1.1.7", "@types/node": "^20.19.10", "@types/node-cron": "^2.0.4", "@types/node-fetch": "^2.5.12", "@types/pg": "^8.6.1", "@types/pizzip": "^3.0.2", "@types/request": "^2.48.7", "@types/request-ip": "^0.0.37", "@types/uuid": "^8.3.4", "nodemon": "^2.0.7", "ts-node": "10.8.1", "ts-node-dev": "^1.1.8"}, "overrides": {"node-forge": "1.3.1"}}