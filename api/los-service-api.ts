import * as common from "../utils/common";
import fs from 'fs';
import * as configService from '../services/config-service';
import * as oauthApi from '../api/oauth-service-api';
import { ResponseUpload } from "../types";

// TODO: deprecate service
export async function contractInfo(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract info: ' + contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response = await common.getAPI(cloudConfig.data.losServiceApi.serviceUrl + cloudConfig.data.losServiceApi.getContractInfoUrl + contractNumber, header);
    common.log('Get contract info response: ' + contractNumber);
    common.log(response);
    return response;
}

export async function contractFile(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract file: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.getFileAPI(cloudConfig.data.losServiceApi.serviceUrl + cloudConfig.data.losServiceApi.getContractFileUrl + contractNumber, header);
        common.log('Get contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadSignedFileContract(contractNumber: string, signedFileDataBuffer: Buffer): Promise<ResponseUpload> {
    let cloudConfig = configService.getConfig();
    common.log('Upload signed file: ' + contractNumber);

    let fileName = './upload_tmp/signed_contract/' + contractNumber + '.pdf';
    try {
        fs.writeFileSync(fileName, signedFileDataBuffer);
        let formData = {
            'LCT': {
                'value': fs.createReadStream(fileName),
                'options': {
                    'filename': fileName,
                    'contentType': null
                }
            }
        };
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(cloudConfig.data.losServiceApi.serviceUrl + cloudConfig.data.losServiceApi.uploadFileSignedUrl + contractNumber, formData, header);
        common.log('Upload signed file response: ' + contractNumber);
        common.log(response);
        return response;
    } catch (error) {
        common.error(error);
        return null;
    } finally {
        fs.unlinkSync(fileName);
    }
}
