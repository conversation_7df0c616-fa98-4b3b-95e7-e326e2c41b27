import * as common from "../utils/common";
import * as configService from '../services/config-service';

interface StatusHistoryRequest {
    contractNumber: string
    systemType: string
    stepCode: string
    actionCode: string
    entryDate: string
    updateStatusDate: string
    title: string
    involveParty: string
    value: string
}
export async function statusHistory(request: StatusHistoryRequest) {
    let cloudConfig = configService.getConfig();
    common.log('Request_case_history:');
    common.log(request);
    let res = await common.postAPI(common.getServiceUrl(cloudConfig.basic.actionAudit) + '/action-audit/v1/status-history', request, { 'Content-Type': 'application/json' });
    common.log('Response_case_history:');
    common.log(res);
    return res;
}
