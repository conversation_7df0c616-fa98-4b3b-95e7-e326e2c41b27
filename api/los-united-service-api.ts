import * as common from "../utils/common";
import fs from 'fs';
import * as configService from '../services/config-service';
import * as oauthApi from './oauth-service-api';
import qs from "querystring";
import { CancelLoanContractLosResponse, ContractInfoLosResponse, RefuseLoanContractLosResponse, ResponseUpload, VerifyLoanLosResponse } from "../types";

type DbrContractInfoResponse = {
    code: number;
    msg: string;
    data?: {
        identity_card: string;
        date_of_birth: Date;
        customer_name: string;
        entry_date: Date;
        tem_address: string;
        permanent_address: string;
        channel: string;
        partner_code: string;
        contract_number?: string;
        phone_number?: string;
        current_province?: string;
    }
}

export async function contractInfo(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract info: ' + contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response: ContractInfoLosResponse = await common.getAPI(
        common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.getContractInfoUrl + contractNumber,
        header
    );
    common.log('Get contract info response: ' + contractNumber);
    common.log(response);
    return response;
}

export async function verifyLoan(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('verifyLoan: ' + contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response: VerifyLoanLosResponse = await common.postAPIv2(
        common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.verifyLoanUrl + contractNumber,
        {},
        header
    );
    common.log('verifyLoan response: ' + contractNumber);
    common.log(response);
    return response;
}

export async function cancelLoanContract(contractNumber: string, decisionCode: string) {
    const cloudConfig = configService.getConfig();
    common.log('cancelLoanContract: ' + contractNumber);
    const tokenInfo = oauthApi.getOauthInfo();
    const header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    const payload = {
        contractList: [contractNumber],
        comment: 'cancel by ESIGNING service',
        act_by: 'ESIGNING',
        type: '2',
        request_by: 'ESIGNING',
        message_code: decisionCode
      }
    const response: CancelLoanContractLosResponse = await common.postAPIv2(
        common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.cancelLoanContractUrl,
        payload,
        header
    );
    common.log('cancelLoanContract response: ' + contractNumber);
    common.log(response);
    return response;
}

export async function refuseLoanContract(contractNumber: string, decisionCode: string) {
    const cloudConfig = configService.getConfig();
    common.log('refuseLoanContract: ' + contractNumber);
    const tokenInfo = oauthApi.getOauthInfo();
    const header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    const payload = {
        contractList: [contractNumber],
        comment: 'call refuse by ESIGNING service',
        act_by: 'ESIGNING',
        type: '2',
        request_by: 'ESIGNING',
        message_code: decisionCode
      }
    const response: RefuseLoanContractLosResponse = await common.postAPIv2(
        common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.refuseLoanContractUrl,
        payload,
        header
    );
    common.log('refuseLoanContract response: ' + contractNumber);
    common.log(response);
    return response;
}


export async function getDocResubmit(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('getDocResubmit:', contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response = await common.getAPI(common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.getDocResubmit + contractNumber, header);
    common.log('Response:');
    common.log(response);
    return response;
}

export async function contractFile(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract file: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.getFileAPI(common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.getContractFileUrl + contractNumber, header);
        response = response ?? '{}';
        response = JSON.parse(response);
        response = response?.data;
        common.log('Get contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function contractFileV2(requestRule) {
    let cloudConfig = configService.getConfig();
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        common.log('request_rule');
        common.log(requestRule);
        let response = await common.postAPI(common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.getContractFileUrlV2, requestRule, header);
        common.log('response_rule');
        common.log({ ...response, data: response?.data?.length });
        return response?.data;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadSignedFileContract(contractNumber: string, signedFileDataBuffer: Buffer): Promise<ResponseUpload> {
    let cloudConfig = configService.getConfig();
    common.log('Upload signed file: ' + contractNumber);
    let fileName = './upload_tmp/signed_contract/' + contractNumber + '.pdf';
    try {
        fs.writeFileSync(fileName, signedFileDataBuffer);
        let formData = {
            'file_signed': fs.createReadStream(fileName),
            'contract_number': contractNumber,
            'status': 'signed'
        };
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(
            common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.uploadFileSignedUrl,
            formData,
            header
        );
        common.log('Upload signed file response: ' + contractNumber);
        common.log(response);
        response = JSON.parse(response);

        return response;
    } catch (error) {
        common.error(error);
        return null;
    } finally {
        fs.unlinkSync(fileName);
    }
}

export async function uploadDocs(contractNumber: string, docs, bundleList, status) {
    let cloudConfig = configService.getConfig();
    common.log('Request uploadDocs: ' + contractNumber);
    common.log(docs);
    let formData = {
        'contract_number': contractNumber,
    };
    for (let doc in docs) {
        formData[doc] = fs.createReadStream(docs[doc]);
    }
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.uploadDocs, formData, header);
        common.log('Response uploadFileFromDataAPI:');
        common.log(response);
        response = JSON.parse(response);
        let dataRes = response?.data?.listDocCollecting ?? [];
        let listAllDocsBundle = [];
        for (let bundle of bundleList) {
            let docList = bundle?.docList ?? [];
            listAllDocsBundle = listAllDocsBundle.concat(docList);
        }
        for (let doc of dataRes) {
            let docName = doc?.docName;
            let docReq = listAllDocsBundle.find(obj => obj && (obj.docType || obj.docName) && (obj.docType == docName || obj.docName == docName));
            doc.docGroup = docReq?.docGroup;
        }
        common.log('dataRes: Los-United');
        common.log(dataRes);
        common.log('--end-dataRes: Los-United');
        let formDataSave = {
            "contractNumber": contractNumber,
            "uploadType": status,
            "docList": dataRes
        };
        let responseSave = await common.postAPI(common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.saveUploadDoc, formDataSave, header);
        common.log('uploadDocs ');
        common.log(responseSave);
        return responseSave;
    } catch (error) {
        common.error(error);
        return null;
    }
}

export async function dbrContractInfo(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract dbr info: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let url = common.getServiceUrl(cloudConfig.basic.losUnited)
            + cloudConfig.data.losUnitedApi.getDbrContractInfo
            + "?" + qs.stringify({ contractNumber, contractType: "CCN" });
        let response: DbrContractInfoResponse = await common.getAPI(url, header);
        common.log('Get contract dbr info response: ' + contractNumber);
        common.log(response);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function dbrContractFiles(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract dbr file: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.postAPI(common.getServiceUrl(cloudConfig.basic.losUnited)
            + cloudConfig.data.losUnitedApi.downloadDbrFiles,
            { contract_number: contractNumber },
            header);
        common.log('Get contract dbr file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadSignedFile(contractNumber: string, signedAnnex, signedPaymentSchedule) {
    let cloudConfig = configService.getConfig();
    common.log('Upload signed dbr file: ' + contractNumber);

    let fileName1 = './upload_tmp/signed_contract/PL_' + contractNumber + '.pdf';
    fs.writeFileSync(fileName1, Buffer.from(signedAnnex.toString(), 'base64'));

    let fileName2 = './upload_tmp/signed_contract/LTT_' + contractNumber + '.pdf';
    fs.writeFileSync(fileName2, Buffer.from(signedPaymentSchedule.toString(), 'base64'));

    let formData = {
        'PLH': fs.createReadStream(fileName1),
        'LTT': fs.createReadStream(fileName2),
        'contract_number': contractNumber,
    };
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(
            common.getServiceUrl(cloudConfig.basic.losUnited) + cloudConfig.data.losUnitedApi.uploadSignedDbrFiles,
            formData,
            header
        );
        common.log('Upload signed dbr file response: ' + contractNumber);
        common.log(response);
        return response;
    } catch (error) {
        common.error(error);
        return null;
    } finally {
        fs.unlinkSync(fileName1);
        fs.unlinkSync(fileName2);
    }
}