import * as configService from '../services/config-service';
import * as common from "../utils/common";

interface GetTokenResponse {
    code?: number;
    message?: string;
    accessToken?: string;
}
export async function getToken(contractNumber: string, idNumber: string): Promise<string> {
    let cloudConfig = configService.getConfig();
    let serviceUrl = common.getServiceUrl(cloudConfig.basic.aaa) + cloudConfig.data.aaaService.loginV3;
    let request = {
        contractNumber,
        idNumber
    }
    let token = await common.postAPI<GetTokenResponse>(serviceUrl, request, { 'Content-Type': 'application/json' });
    let tokenData = token?.accessToken ?? '';
    return tokenData;
}

interface ValidateTokenResponse {
    code?: number;
    message?: string;
    data?: {
        uuid: string;
        contractNumber: string;
        idNumber: string;
        createdTime: string;
    }
}
export async function validateToken(token: string, contractNumber: string): Promise<boolean> {
    if (!token)
        return false;
    let cloudConfig = configService.getConfig();
    let serviceUrl = common.getServiceUrl(cloudConfig.basic.aaa) + cloudConfig.data.aaaService.validateTokenV3;

    let header = {
        'access-token': token
    }
    let response = await common.getAPI<ValidateTokenResponse>(serviceUrl, header);
    return response?.data?.contractNumber === contractNumber;
}

export type RouteAuthenResponse = {
    statusCode: number
    status: number
    code: number
    message: string
    name: string
    isConfig: number
}
export async function authorizeRouter(partnerCode: string) {
    try {
        let cloudConfig = configService.getConfig();
        let url = common.getServiceUrl(cloudConfig.basic.aaa) + "/aaa/v1/sale/authenticate"
        let response = await common.getAPI<RouteAuthenResponse>(url, {
            check_token: '0',
            service: 'ESIGNING',
            partner_code: partnerCode,
            service_type: 'CHECK'
        });
        return response;
    } catch (error) { }
}