import { configService } from "../services";
import * as common from "../utils/common";

export async function downloadFile(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract file: ' + contractNumber);
    try {
        let path = common.getServiceUrl(cloudConfig.basic.convertService)
            + '/convert-service/v1/esigning/download?contractNumber=' + contractNumber;
        let response = await common.getAPI(path, {});
        response = response?.result;
        common.log('Get contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadFile(contractNumber: string, file: Buffer) {
    let cloudConfig = configService.getConfig();
    common.log('Upload contract file: ' + contractNumber);
    try {
        let path = common.getServiceUrl(cloudConfig.basic.convertService)
            + '/convert-service/v1/esigning/customer/upload-signed';
        let response = await common.postAPI(path, {
            contractNumber: contractNumber,
            fileData: file.toString('base64')
        }, { 'Content-Type': 'application/json' });
        response = response?.result;
        common.log('Upload contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}