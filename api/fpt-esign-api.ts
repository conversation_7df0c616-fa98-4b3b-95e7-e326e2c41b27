import * as common from '../utils/common';
import * as configService from '../services/config-service';
import { FptResponse } from '../types';
import { getContractSystemType } from '../utils/contract-utils';
import { fileService } from '../services';

//api name
const prepareCertificate = 'prepareCertificateForSignCloud';
const prepareFile = 'prepareFileForSignCloud';
const authorizeSingletonSigning = 'authorizeSingletonSigningForSignCloud';
const regenerateAuthorizationCode = 'regenerateAuthorizationCodeForSignCloud';
const getSignedFile = 'getSignedFileForSignCloud';

//AUTHORISATION_METHOD
const AUTHORISATION_METHOD_SMS = 1;
const AUTHORISATION_METHOD_EMAIL = 2;
const AUTHORISATION_METHOD_MOBILE = 3;
const AUTHORISATION_METHOD_PASSCODE = 4;
const AUTHORISATION_METHOD_UAF = 5;

//mine type
const MIMETYPE_PDF = "application/pdf";
const MIMETYPE_XML = "application/xml";
const MIMETYPE_XHTML_XML = "application/xhtml+xml";

//
const SYNCHRONOUS = 3;

export async function prepareCertificateForSignCloud(agreementUUID: string, contractInfo: {
    customerName: string,
    contractNumber: string,
    identityCardId: string,
    customerAddress: string,
    phoneNumber: string,
    images?: {
        idFront: string,
        idBack: string,
        portrait: string,
    },
}): Promise<FptResponse> {
    let cloudConfig = configService.getConfig();

    let credentialData = createCredentialData(cloudConfig);

    const images = await Promise.all([
        fileService.downloadLosUnitedFile(contractInfo.images?.idFront).then(data => data.toString('base64')),
        fileService.downloadLosUnitedFile(contractInfo.images?.idBack).then(data => data.toString('base64')),
        fileService.downloadLosUnitedFile(contractInfo.images?.portrait).then(data => data.toString('base64')),
    ]);

    let agreementDetails = {
        personalName: contractInfo.customerName,
        organizationUnit: contractInfo.contractNumber,
        personalID: contractInfo.identityCardId,
        location: contractInfo.customerAddress,
        stateOrProvince: contractInfo.customerAddress,
        country: 'VN',
        photoFrontSideIDCard: images[0],
        photoBackSideIDCard: images[1],
        faceImage: images[2],
    };

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: agreementUUID,
        credentialData: credentialData,
        mobileNo: contractInfo.phoneNumber,
        agreementDetails: agreementDetails,
        timestampEnabled: false,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: 1,
        messagingMode: 0,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log('Prepare certificate: ' + contractInfo.contractNumber);
    common.log({
        ...signCloudReq, agreementDetails: {
            ...agreementDetails, photoFrontSideIDCard: undefined,
            photoBackSideIDCard: undefined,
            faceImage: undefined,
        }
    });
    let res = null;
    try {
        res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + prepareCertificate, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        common.log('prepareCertificateForSignCloud response code = ' + res.responseCode);
        common.log('prepareCertificateForSignCloud response message = ' + res.responseMessage);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }
}

export async function prepareFileForSignCloud(agreementUUID: string, fileData: string, contractNumber: string, partnerCode?: string) {
    let cloudConfig = configService.getConfig();
    let timeStamp = new Date().getTime();
    let credentialData = createCredentialData(cloudConfig);

    let checkDr = contractNumber.includes("_");
    const systemType = getContractSystemType(contractNumber);

    // -- SingletonSigning (Signature properties for customer)
    let singletonSigning = {
        PAGENO: 'Last',
        POSITIONIDENTIFIER: 'BÊN VAY',
        RECTANGLEOFFSET: '-40,-120',
        RECTANGLESIZE: '190,90',
        COORDINATE: undefined,
        VISIBLESIGNATURE: 'True',
        VISUALSTATUS: 'False',
        IMAGEANDTEXT: 'False',
        TEXTDIRECTION: 'LEFTTORIGHT',
        SHOWSIGNERINFO: 'True',
        SIGNERINFOPREFIX: 'Ký bởi:',
        SHOWDATETIME: 'True',
        DATETIMEPREFIX: 'Ký ngày:',
        SHOWREASON: 'True',
        SIGNREASONPREFIX: 'Lý do:',
        SIGNREASON: 'Đồng ý giao kết Hợp đồng tín dụng',
        SHOWLOCATION: 'False',
        LOCATION: 'Hà Nội',
        LOCATIONPREFIX: 'Nơi ký:',
        TEXTCOLOR: 'black',
        LOCKAFTERSIGNING: 'False',
        SHOWPERSONALID: 'True',
        PERSONALIDPREFIX: 'CMND/CCCD:',
        SHOWSERIALNUMBER: 'True',
        SERIALNUMBERPREFIX: 'Số hiệu chứng thư số:',
        SHADOWSIGNATUREPROPERTIES: undefined,
    };

    if (checkDr) {
        singletonSigning.RECTANGLEOFFSET = '-40,-100';
    } else if (systemType === "credit") {
        singletonSigning.RECTANGLEOFFSET = '-40,-100';
    } else if (systemType === "merchant") {
        singletonSigning.RECTANGLEOFFSET = '-40,-120';
    } else if (systemType === "vinfast") {
        singletonSigning.POSITIONIDENTIFIER = 'KHÁCH HÀNG';
        singletonSigning.RECTANGLEOFFSET = '-40,-120';
    } else if (systemType === "united") {
        singletonSigning.PAGENO = '4';
        if (partnerCode === 'FIZ') {
            singletonSigning.PAGENO = '6';
            singletonSigning.RECTANGLEOFFSET = '0,-120';
        } else if (partnerCode === 'EVC_HM') {
            singletonSigning.PAGENO = 'Last';
        } else if (partnerCode === 'EVC_WD') {
            singletonSigning.PAGENO = '6';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '100,240,300,340';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=19/COORDINATE=380,450,580,550';
        } else if (partnerCode === 'DNSE_HM') {
            singletonSigning.PAGENO = '11';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '120,500,320,600';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=14/COORDINATE=350,180,550,280;PAGENO=2/COORDINATE=350,580,550,680';
        } else if (partnerCode === 'DNSE_WD' || partnerCode === 'MANF_LD') {
            singletonSigning.PAGENO = 'Last';
        } else if (partnerCode === 'GIMO_WD') {
            singletonSigning.PAGENO = '4';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '100,50,300,150';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=15/COORDINATE=340,175,540,275';
        } else if (partnerCode === 'MANF_WD') {
            singletonSigning.PAGENO = '3';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '90,545,290,645';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=15/COORDINATE=360,195,560,295;PAGENO=19/COORDINATE=345,65,545,165;PAGENO=28/COORDINATE=320,430,520,530';
        } else if (partnerCode === 'FUND_WD') {
            singletonSigning.PAGENO = '5';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '100,110,300,210';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=16/COORDINATE=360,500,560,600';
        } else if (partnerCode === 'VUIP_HM') {
            singletonSigning.PAGENO = '8';
            singletonSigning.RECTANGLEOFFSET = undefined;
            singletonSigning.COORDINATE = '120,305,320,405';
            singletonSigning.SHADOWSIGNATUREPROPERTIES = 'PAGENO=11/COORDINATE=365,150,565,250';
        } else if (partnerCode === 'VUIP_WD') {
            singletonSigning.PAGENO = 'Last';
        }
    }

    let signCloudMetaData = {
        singletonSigning: singletonSigning
    };

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: agreementUUID,
        signingFileData: fileData,
        mimeType: MIMETYPE_PDF,
        signingFileName: 'HDDT-' + timeStamp + '.pdf',
        notificationTemplate: 'EC: Your Authorize Code: {AuthorizeCode}. It will be expired within 5 minutes',
        notificationSubject: 'EC - Authorization Code',
        credentialData: credentialData,
        signCloudMetaData: signCloudMetaData,
        timestampEnabled: false,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: AUTHORISATION_METHOD_SMS,
        messagingMode: 0,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log(JSON.stringify({ ...signCloudReq, signingFileData: "<base64>" }), "prepareFileForSignCloud");
    let res = null;
    try {
        res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + prepareFile, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        common.log('prepareFileForSignCloud resposne code = ' + res.responseCode);
        common.log('prepareFileForSignCloud response message = ' + res.responseMessage);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }
}

export async function authorizeCounterSigningForSignCloud(agreementUUID: string, authorizeCode: string, billCode: string) {
    let cloudConfig = configService.getConfig();
    let credentialData = createCredentialData(cloudConfig);

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: agreementUUID,
        billCode: billCode,
        authorizeCode: authorizeCode,
        ltvEnabled: false,
        credentialData: credentialData,
        timestampEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: 1,
        messagingMode: SYNCHRONOUS,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log('authorizeCounterSigningForSignCloud: ' + agreementUUID);
    common.log(signCloudReq);
    let res = null;
    try {
        res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + authorizeSingletonSigning, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        // console.log(res);
        common.log('authorizeCounterSigningForSignCloud resposne code = ' + res.responseCode);
        common.log('authorizeCounterSigningForSignCloud response message = ' + res.responseMessage);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }
}

export async function prepareFileForEasyCreditSignCloud(fileData: string, contractNumber: string, partnerCode?: string) {
    let cloudConfig = configService.getConfig();
    let timeStamp = new Date().getTime();
    let credentialData = createCredentialData(cloudConfig);
    let checkDr = contractNumber.includes("_");
    const systemType = getContractSystemType(contractNumber);
    // -- SingletonSigning (Signature properties for EC)
    let singletonSigning = {
        PAGENO: 'Last',
        POSITIONIDENTIFIER: 'CÔNG TY TÀI CHÍNH CỔ PHẦN ĐIỆN LỰC',
        RECTANGLEOFFSET: '-20,-120',
        RECTANGLESIZE: '190,90',
        VISIBLESIGNATURE: 'True',
        VISUALSTATUS: 'False',
        IMAGEANDTEXT: 'False',
        TEXTDIRECTION: 'LEFTTORIGHT',
        SHOWSIGNERINFO: 'True',
        SIGNERINFOPREFIX: 'Ký bởi:',
        SHOWDATETIME: 'True',
        DATETIMEPREFIX: 'Ký ngày:',
        SHOWREASON: 'True',
        SIGNREASONPREFIX: 'Lý do:',
        SIGNREASON: 'Đồng ý giao kết Hợp đồng tín dụng',
        SHOWLOCATION: 'False',
        LOCATION: 'Hà Nội',
        LOCATIONPREFIX: 'Nơi ký:',
        TEXTCOLOR: 'black',
        LOCKAFTERSIGNING: 'False',
        SHOWORGANIZATION: 'True',
        ORGANIZATIONPREFIX: 'Thuộc tổ chức:',
        SHOWSERIALNUMBER: 'True',
        SERIALNUMBERPREFIX: 'Số hiệu chứng thư số:'
    };

    if (partnerCode === "MISA") {
        singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
        singletonSigning.RECTANGLEOFFSET = '-20,-140';
    } else if (partnerCode === "MISA_SK") {
        singletonSigning.POSITIONIDENTIFIER = 'VŨ TRÍ CÔNG';
        singletonSigning.RECTANGLEOFFSET = '-40,0';
        singletonSigning.RECTANGLESIZE = '190,60';
    } else if (partnerCode === "MISA_HM" || partnerCode === "MISA_HDHM") {
        singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
        singletonSigning.RECTANGLEOFFSET = '-70,-140';
        singletonSigning.RECTANGLESIZE = '240,55';
    } else if (partnerCode === "MISA_BCTD") {
        singletonSigning.RECTANGLEOFFSET = '0,-150';
        singletonSigning.SIGNREASON = 'Đồng ý cho vay';
    } else if (partnerCode === "MISA_VVNH") {
        singletonSigning.POSITIONIDENTIFIER = 'NGƯỜI ĐẠI DIỆN';
        singletonSigning.RECTANGLEOFFSET = '20,-100';
        singletonSigning.SIGNREASON = 'Đồng ý cho vay';
    } else if (partnerCode === "DNSE_TTRV") {
        singletonSigning.POSITIONIDENTIFIER = 'CÔNG TY TÀI CHÍNH CỔ PHẦN ĐIỆN LỰC';
        singletonSigning.RECTANGLEOFFSET = '20,-150';
        singletonSigning.SIGNREASON = 'Đồng ý cho vay';
        singletonSigning.PAGENO = '2';
    } else if (partnerCode === "DNSE_BCTD" || partnerCode === "GIMO_BCTD" || partnerCode === "FUND_BCTD" || partnerCode === "MANF_BCTD") {
        singletonSigning.POSITIONIDENTIFIER = 'CÔNG TY TÀI CHÍNH CỔ PHẦN ĐIỆN LỰC';
        singletonSigning.RECTANGLEOFFSET = '20,-150';
        singletonSigning.SIGNREASON = 'Đồng ý cho vay';
        singletonSigning.PAGENO = 'Last';
    } else if (partnerCode === "DNSE_LD" || partnerCode === "GIMO_LD" || partnerCode === "FUND_LD" || partnerCode === "MANF_LD" || partnerCode === "VUIP_LD") {
        singletonSigning.POSITIONIDENTIFIER = 'Đại diện Công ty';
        singletonSigning.RECTANGLESIZE = '150,90';
        singletonSigning.RECTANGLEOFFSET = '90,-90';
        singletonSigning.SIGNREASON = 'Đồng ý cho vay';
        singletonSigning.PAGENO = 'Last';
    } else if (checkDr || systemType === "credit") {
        singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
        singletonSigning.RECTANGLEOFFSET = '-30,-120';
    } else if (systemType === "merchant") {
        singletonSigning.POSITIONIDENTIFIER = 'CÔNG TY TÀI CHÍNH CỔ PHẦN ĐIỆN LỰC';
        singletonSigning.RECTANGLEOFFSET = '20,-120';
    } else if (systemType === "vinfast" || systemType === "cashloan") {
        singletonSigning.POSITIONIDENTIFIER = 'CÔNG TY TÀI CHÍNH CỔ PHẦN';
        singletonSigning.RECTANGLEOFFSET = '10,-120';
    } else if (systemType === "united") {
        singletonSigning.PAGENO = '4';
        singletonSigning.POSITIONIDENTIFIER = 'CÔNG TY TÀI CHÍNH CỔ PHẦN';
        singletonSigning.RECTANGLEOFFSET = '10,-120';
        if (partnerCode === 'FIZ') {
            singletonSigning.PAGENO = '6';
        } else if (partnerCode === 'EVC_HM') {
            singletonSigning.PAGENO = 'Last';
            singletonSigning.RECTANGLEOFFSET = '10,-100';
        } else if (partnerCode === 'EVC_WD') {
            singletonSigning.PAGENO = '6';
            singletonSigning.RECTANGLEOFFSET = '40,-120';
        } else if (partnerCode === 'DNSE_HM') {
            singletonSigning.PAGENO = '11';
            singletonSigning.RECTANGLEOFFSET = '10,-110';
        } else if (partnerCode === 'DNSE_WD') {
            singletonSigning.PAGENO = 'Last';
            singletonSigning.RECTANGLEOFFSET = '10,-90';
        } else if (partnerCode === 'GIMO_WD') {
            singletonSigning.PAGENO = '5';
            singletonSigning.RECTANGLEOFFSET = '20,-110';
        } else if (partnerCode === 'MANF_WD') {
            singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
            singletonSigning.PAGENO = '3';
            singletonSigning.RECTANGLEOFFSET = '-10,-105';
        } else if (partnerCode === 'FUND_WD') {
            singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
            singletonSigning.PAGENO = '5';
            singletonSigning.RECTANGLEOFFSET = '-30,-120';
        } else if (partnerCode === 'VUIP_HM') {
            singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
            singletonSigning.PAGENO = '8';
            singletonSigning.RECTANGLEOFFSET = '-30,-120';
        } else if (partnerCode === 'VUIP_WD') {
            singletonSigning.POSITIONIDENTIFIER = 'BÊN CHO VAY';
            singletonSigning.PAGENO = '2';
            singletonSigning.RECTANGLEOFFSET = '-30,-115';
        }
    }
    let signCloudMetaData = {
        singletonSigning: singletonSigning
    };

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: cloudConfig.data.fptEsignApi.agreementUuid,
        authorizeCode: cloudConfig.data.fptEsignApi.passCode,
        signingFileData: fileData,
        mimeType: MIMETYPE_PDF,
        signingFileName: 'HDDT-' + timeStamp + '.pdf',
        signCloudMetaData: signCloudMetaData,
        credentialData: credentialData,
        timestampEnabled: false,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: AUTHORISATION_METHOD_PASSCODE,
        messagingMode: 0,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log(JSON.stringify({ ...signCloudReq, signingFileData: "<base64>" }), "prepareFileForEasyCreditSignCloud");
    try {
        let res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + prepareFile, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        common.log('prepareFileForEasyCreditSignCloud response code = ' + res.responseCode);
        common.log('prepareFileForEasyCreditSignCloud response message = ' + res.responseMessage);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }
}

export async function regenerateAuthorizationCodeForSignCloud(agreementUUID: string) {
    let cloudConfig = configService.getConfig();
    let credential_data = createCredentialData(cloudConfig);

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: agreementUUID,
        credentialData: credential_data,
        notificationTemplate: 'EC: Your Authorize Code: {AuthorizeCode}. It will be expired within 5 minutes',
        notificationSubject: 'EC - Authorization Code',
        timestampEnabled: false,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: AUTHORISATION_METHOD_SMS,
        messagingMode: 0,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log('regenerateAuthorizationCodeForSignCloud: ' + agreementUUID);
    common.log(signCloudReq);
    let res = null;
    try {
        res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + regenerateAuthorizationCode, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        common.log('regenerateAuthorizationCodeForSignCloud response code = ' + res.responseCode);
        common.log('regenerateAuthorizationCodeForSignCloud response message = ' + res.responseMessage);
        // console.log(res);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }

}

export async function getSignedFileForSignCloud(agreementUUID: string) {
    let cloudConfig = configService.getConfig();
    let credential_data = createCredentialData(cloudConfig);

    let signCloudReq = {
        relyingParty: cloudConfig.data.fptEsignApi.relyingParty,
        agreementUUID: agreementUUID,
        credentialData: credential_data,
        timestampEnabled: false,
        ltvEnabled: false,
        postbackEnabled: false,
        noPadding: false,
        authorizeMethod: 0,
        messagingMode: 0,
        sharedMode: 0,
        p2pEnabled: false,
        csrRequired: false,
        certificateRequired: false,
        keepOldKeysEnabled: false,
        revokeOldCertificateEnabled: false
    };

    common.log('getSignedFileForSignCloud: ' + agreementUUID);
    common.log(signCloudReq);
    let res = null;
    try {
        res = await common.postAPI(cloudConfig.data.fptEsignApi.serviceUrl + getSignedFile, signCloudReq, { 'Content-Type': 'application/json' });
        common.log('Response:');
        common.log('getSignedFileForSignCloud response code = ' + res.responseCode);
        common.log('getSignedFileForSignCloud response message = ' + res.responseMessage);
        return res;
    } catch (error) {
        common.error(error);
        return null;
    }
}

function createCredentialData(cloudConfig: configService.CloudConfig) {
    let time_stamp = new Date().getTime();
    let dataToSign = cloudConfig.data.fptEsignApi.relyingPartyUser + cloudConfig.data.fptEsignApi.relyingPartyPassword + cloudConfig.data.fptEsignApi.relyingPartySignature + time_stamp;
    let signature = common.getPKCS1Signature(dataToSign, cloudConfig.data.fptEsignApi.relyingPartyKeyStore, cloudConfig.data.fptEsignApi.relyingPartyKeyStorePassword);
    common.log('dataToSign: ' + dataToSign);

    let credentialData = {
        username: cloudConfig.data.fptEsignApi.relyingPartyUser,
        password: cloudConfig.data.fptEsignApi.relyingPartyPassword,
        timestamp: time_stamp,
        signature: cloudConfig.data.fptEsignApi.relyingPartySignature,
        pkcs1Signature: signature
    };
    return credentialData;
}
