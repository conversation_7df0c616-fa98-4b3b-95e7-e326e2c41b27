import { configService, fileService } from "../services";
import * as common from "../utils/common";
import { URLSearchParams } from "url";
import fetch from "node-fetch";
import { v4 as uuidv4 } from 'uuid';

export async function getMapImage(latitude: number, longitude: number, zoomLevel: number) {
    const config = configService.getConfig();
    const apiKey = config.data.googleApi.apiKey;

    let params = new URLSearchParams();
    params.append("center", `${latitude},${longitude}`);
    params.append("zoom", zoomLevel.toString());
    params.append("size", "640x640");
    params.append("maptype", "satellite");
    params.append("key", apiKey);
    params.append("markers", `color:red|${latitude},${longitude}`);

    const url = "https://maps.googleapis.com/maps/api/staticmap?" + params.toString();
    try {
        const res = await fetch(url);
        const buffer = await res.buffer();
        const image_id = 'esigning/maps/' + uuidv4() + '.png';
        common.log('Map image id: ' + image_id);
        await fileService.uploadFile(image_id, buffer);
        return image_id;
    } catch (error) {
        common.error(error);
    }
}