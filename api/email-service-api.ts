import * as common from '../utils/common';
import * as configService from '../services/config-service';

export async function sendEmail(request: any) {
    let cloudConfig = configService.getConfig();
    common.log('sendEmail Request:');
    try {
        let link = common.getServiceUrl(cloudConfig.basic.bssEmailService) + "/email/v1";
        let res = await common.postAPI(link, request, { 'Content-Type': 'application/json' });
        common.log('sendEmail Response:');
        common.log(res);
        return res;
    } catch (error) {
        common.log('send email error: ', error);
        common.error(error);
        return null;
    }
}
