import * as common from '../utils/common';
import * as configService from '../services/config-service';
import cron from 'node-cron';

let oauthInfo: AuthenResponse;

type AuthenResponse = {
    status: number,
    token: string,
    uiid: number,
};
export async function loadOauthInfo() {
    let cloudConfig = configService.getConfig();
    let request = {
        username: cloudConfig.data.oauthService.username,
        password: cloudConfig.data.oauthService.password
    };
    let tokenData: AuthenResponse = await common.postAPI(common.getServiceUrl(cloudConfig.basic.aaa) + "/aaa/v02/authen/signin", request, { 'Content-Type': 'application/json' });
    if (!process.env.DEBUG) {
        common.log(tokenData);
    }
    oauthInfo = tokenData;
    return tokenData;
}

export function getOauthInfo() {
    return oauthInfo;
}

cron.schedule('*/10 * * * *', () => {
    common.log('Keep LMS token service alive...');
    loadOauthInfo();
})
