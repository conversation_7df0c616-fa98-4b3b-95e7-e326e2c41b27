import * as common from "../utils/common";
import * as configService from '../services/config-service';
import * as oauthApi from './oauth-service-api';
import NodeCache from "node-cache";

const cache = new NodeCache();

export async function docInfo(docType: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get doc info: ' + docType);
    // check cache
    let value = cache.get(docType);
    if (value) {
        common.log("load from cache");
        return value;
    }
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response = await common.getAPI(common.getServiceUrl(cloudConfig.basic.masterData) + cloudConfig.data.masterData.docInfo + docType, header);
    common.log('Get doc info response: ' + docType);
    common.log(response);
    // save cache for 1 hour
    cache.set(docType, response, 3600);
    return response;
}