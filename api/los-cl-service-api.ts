import * as common from '../utils/common';
import fs from 'fs';
import * as configService from '../services/config-service';
import * as oauthApi from './oauth-service-api';
import { ContractInfoLosResponse, ResponseUpload } from '../types';

export async function contractInfo(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract info: ' + contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response: ContractInfoLosResponse = await common.getAPI(
        common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.getContractInfoUrl + contractNumber,
        header
    );
    common.log('Get contract info response: ' + contractNumber);
    common.log(response);
    return response;
}
export async function getDocResubmit(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('getDocResubmit:', contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response = await common.getAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.getDocResubmit + contractNumber, header);
    common.log('Response:');
    common.log(response);
    return response;
}

export async function contractFile(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract file: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.getFileAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.getContractFileUrl + contractNumber, header);
        response = response ? response : '{}';
        response = JSON.parse(response);
        response = response && response.data ? response.data : null;
        common.log('Get contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function contractFileV2(requestRule) {
    let cloudConfig = configService.getConfig();
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        common.log('Get contract file v2');
        common.log(requestRule);
        let response = await common.postAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.getContractFileUrlV2, requestRule, header);
        common.log('Get contract file v2 response');
        common.log({ ...response, data: response?.data?.length });
        return response?.data;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadSignedFileContract(contractNumber: string, signedFileDataBuffer: Buffer): Promise<ResponseUpload> {
    let cloudConfig = configService.getConfig();
    common.log('Upload signed file: ' + contractNumber);
    let fileName = './upload_tmp/signed_contract/' + contractNumber + '.pdf';
    try {
        fs.writeFileSync(fileName, signedFileDataBuffer);
        let formData = {
            'file_signed': fs.createReadStream(fileName),
            'contract_number': contractNumber,
            'status': 'signed'
        };
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.uploadFileSignedUrl, formData, header);
        common.log('Upload signed file response: ' + contractNumber);
        common.log(response);
        response = JSON.parse(response);

        return response;
    } catch (error) {
        common.error(error);
        return null;
    } finally {
        fs.unlinkSync(fileName);
    }
}

export async function uploadDocs(contractNumber: string, docs, bundleList, status) {
    let cloudConfig = configService.getConfig();
    common.log('Request uploadDocs: ' + contractNumber);
    common.log(docs);
    try {
        let formData = {
            'contract_number': contractNumber,
        };
        for (let doc in docs) {
            formData[doc] = fs.createReadStream(docs[doc]);
        }
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.uploadFileFromDataAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.uploadDocs, formData, header);
        common.log('Response uploadFileFromDataAPI:');
        common.log(response);
        response = JSON.parse(response);
        let dataRes = response?.data?.listDocCollecting ?? [];
        let listAllDocsBundle = [];
        for (let bundle of bundleList) {
            let docList = bundle?.docList ?? [];
            listAllDocsBundle = listAllDocsBundle.concat(docList);
        }
        for (let doc of dataRes) {
            let docName = doc?.docName;
            let docReq = listAllDocsBundle.find(obj => obj && (obj.docType || obj.docName) && (obj.docType == docName || obj.docName == docName));
            doc.docGroup = docReq && docReq.docGroup ? docReq.docGroup : null;
        }
        common.log('dataRes: Los-CL');
        common.log(dataRes);
        common.log('--end-dataRes: Los-CL');
        let formDataSave = {
            "contractNumber": contractNumber,
            "uploadType": status,
            "docList": dataRes
        };
        let responseSave = await common.postAPI(common.getServiceUrl(cloudConfig.basic.losCashloan) + cloudConfig.data.losCLServiceApi.saveUploadDoc, formDataSave, header);
        common.log('uploadDocs ');
        common.log(responseSave);
        return responseSave;
    } catch (error) {
        common.error(error);
        return null;
    }
}
