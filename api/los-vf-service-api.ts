import * as common from "../utils/common";
import * as configService from '../services/config-service';
import * as oauthApi from './oauth-service-api';
import { ContractInfoLosResponse, ResponseUpload } from "../types";
import fs from "fs";

export async function contractInfo(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract info: ' + contractNumber);
    let tokenInfo = oauthApi.getOauthInfo();
    let header = {
        'Content-Type': 'application/json',
        'token': tokenInfo.token,
        'uiid': tokenInfo.uiid
    };
    let response: { responseBody?: ContractInfoLosResponse }
        = await common.getAPI(common.getServiceUrl(cloudConfig.basic.CDL) + cloudConfig.data.losVFServiceApi.getContractInfoUrl + contractNumber, header);
    let responseBody = response?.responseBody;
    common.log('Get contract info response: ' + contractNumber);
    common.log(responseBody);
    return responseBody;
}

export async function contractFile(contractNumber: string) {
    let cloudConfig = configService.getConfig();
    common.log('Get contract file: ' + contractNumber);
    try {
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        let response = await common.getFileAPI(common.getServiceUrl(cloudConfig.basic.CDL) + cloudConfig.data.losVFServiceApi.getContractFileUrl + contractNumber + cloudConfig.data.losVFServiceApi.contractFile, header);
        common.log('Get contract file response: ' + contractNumber);
        return response;
    } catch (error) {
        common.log(error.message);
        common.error(error);
        return null;
    }
}

export async function uploadSignedFileContract(contractNumber: string, signedFileDataBuffer: Buffer): Promise<ResponseUpload> {
    let cloudConfig = configService.getConfig();
    common.log('Upload signed file: ' + contractNumber);

    let fileName = './upload_tmp/signed_contract/' + contractNumber + '.pdf';
    // let buffer = Buffer.from(signedFileData, 'base64');
    try {
        fs.writeFileSync(fileName, signedFileDataBuffer);
        let formData = {
            'LCT': signedFileDataBuffer.toString("base64"),
            'contractNumber': contractNumber,
            'status': 'signed'
        };
        let tokenInfo = oauthApi.getOauthInfo();
        let header = {
            'Content-Type': 'application/json',
            'token': tokenInfo.token,
            'uiid': tokenInfo.uiid
        };
        await common.uploadFileFromDataAPI(common.getServiceUrl(cloudConfig.basic.CDL) + cloudConfig.data.losVFServiceApi.uploadFileSignedUrl, formData, header);
        common.log('Upload signed file response: ' + contractNumber);
        // console.log('uploadSignedFileContract ', response);
        // fs.unlinkSync(fileName);
        return null;
    } catch (error) {
        common.error(error);
        return null;
    } finally {
        fs.unlinkSync(fileName);
    }
}