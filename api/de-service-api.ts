import { configService } from "../services";
import { BUCKET_NAME } from "../services/file-service";
import { ContractInfoEntity } from "../types";
import * as common from "../utils/common";
import { v4 as uuidV4 } from "uuid";

export async function faceVerify(selfie: string, idCard: string, contractNumber: string, bucket: string, contract: ContractInfoEntity) {
    let cloudConfig = configService.getConfig();
    common.log('face verify request:');
    try {
        let link = common.getServiceUrl(cloudConfig.basic.decisionsV02) + "/de-score/v1/ekyc/face-verify";
        const body: any = { selfie, idCard, contractNumber, bucket, requestId: contract.request_id, partnerCode: contract.partner_code };
        if (contract?.spic_path) {
            body.spic = {
                path: contract.spic_path,
                bucket: "ms-los-ap-southeast-1-************-document" // hard code for mc
            }
        }
        let res: { code: number, msg: string } =
            await common.postAPI(link, body, {
                'Content-Type': 'application/json',
                'client_id': 'esigning',
                'request_id': uuidV4(),
                'service_name': 'EKYC'
            });
        common.log('face verify response:');
        common.log(res);
        return res;
    } catch (error) {
        common.log('face verify error: ', error);
        common.error(error);
        return null;
    }
}

export async function faceVerifyV2(selfiePath: string, contractNumber: string,contract: ContractInfoEntity) {
    let cloudConfig = configService.getConfig();
    common.log('face verify v2 request:');
    try {
        const link = common.getServiceUrl(cloudConfig.basic.antiFraud) + "/anti_fraud/v1/ekyc/check_face_matching";
        const requestId = uuidV4();
        const body = {
          case_id: requestId,
          contract_number: contractNumber,
          contract_number_parent: '',
          cust_id: '',
          phone_number: contract.phone_number,
          partner_code: contract.partner_code,
          s3_selfie_image_url: selfiePath,
          s3_selfie_image_bucket: BUCKET_NAME,
          s3_id_card_url: contract.spic_path
        };
        const headers = {
          'Content-Type': 'application/json',
          'service-name': 'EKYC',
          flow: 'CHECK_FACE_MATCHING',
          'client-id': 'esigning',
          'request-id': requestId
        };
        common.log('face verify v2 body:', JSON.stringify(body));
        const res: { code: number, message: string, is_valid: number } =
            await common.postAPIv2(link, body, headers);

        common.log('face verify v2 response:');
        common.log(res);
        return res;
    } catch (error) {
        common.log('face verify v2 error: ', error);
        common.error(error);
        return null;
    }
}