import * as common from "../utils/common";
import * as configService from '../services/config-service';

export async function sendSms(request) {
    try {
        let cloudConfig = configService.getConfig();
        common.log(request, 'Send sms request:');
        let res = await common.postAPI(common.getServiceUrl(cloudConfig.basic.sms) + "/sms/v1/send",
            request,
            { 'Content-Type': 'application/json' });
        common.log(res, 'Send sms response:');
        return res;
    } catch (error) {
        common.log("send sms error: " + error.message);
        common.error(error);
    }
}