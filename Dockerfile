FROM node:20-alpine
RUN apk update
RUN apk add poppler-utils
RUN pdfsig -v

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./
COPY patches/ ./patches/

#RUN npm install
RUN npm install --legacy-peer-deps

COPY . .

# Bundle app source
RUN npm run build

# EXPOSE 1000

CMD [ "npm", "run", "start-dev" ]
